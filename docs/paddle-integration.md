# Paddle Payment Integration for VoiceHype

This document outlines the complete Paddle payment integration for VoiceHype, supporting flexible credit purchasing with amounts between $5 and $95.

## Overview

VoiceHype uses Paddle for payment processing with the following features:
- **Flexible Credit Purchasing**: Users can buy any amount of credits between $5-$95
- **Automatic Credit Addition**: Credits are automatically added after successful payment
- **Secure Webhooks**: Paddle webhooks handle payment confirmations
- **Transaction History**: Complete payment history tracking

## Architecture

### Database Schema (Paddle Schema)

The integration uses a dedicated `paddle` schema with the following tables:

- **`paddle.customers`**: Links VoiceHype users to Paddle customers
- **`paddle.products`**: Stores the main VoiceHype credit product
- **`paddle.transactions`**: Records all payment transactions
- **`paddle.subscriptions`**: For future subscription features
- **`paddle.webhooks`**: Logs webhook events for debugging

### Edge Functions

1. **`paddle-webhook`**: Handles Paddle webhook events
2. **`create-paddle-checkout`**: Creates checkout sessions
3. **`get-paddle-transactions`**: Retrieves user transaction history

## Environment Variables

Add these environment variables to your Supabase project:

```bash
# Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_ENVIRONMENT=sandbox  # or 'production'
PADDLE_WEBHOOK_SECRET=your_webhook_secret_here

# Frontend URL for redirects
FRONTEND_URL=https://voicehype.netlify.app

# Environment flag
ENVIRONMENT=development  # or 'production'
```

## Paddle Setup

### 1. Create Paddle Account
1. Sign up at [Paddle.com](https://paddle.com)
2. Complete business verification
3. Get your API keys from the Developer section

### 2. Create Product in Paddle
1. Go to Catalog → Products in Paddle dashboard
2. Create a new product:
   - **Name**: VoiceHype Credits
   - **Type**: One-time purchase
   - **Pricing**: Variable pricing (allow custom amounts)
   - **Product ID**: `prod_voicehype_credits`

### 3. Configure Webhooks
1. Go to Developer Tools → Webhooks
2. Add webhook endpoint: `https://your-supabase-url.supabase.co/functions/v1/paddle-webhook`
3. Select events:
   - `transaction.completed`
   - `transaction.updated`
   - `customer.created`
   - `customer.updated`

## Database Migration

Run the migration to set up the Paddle schema:

```sql
-- Apply the migration
\i supabase/migrations/20250122_001_paddle_integration.sql
```

## Frontend Integration

### Credit Purchase Flow

1. User clicks "Buy Credits" and enters amount ($5-$95)
2. Frontend calls `create-paddle-checkout` edge function
3. User is redirected to Paddle checkout
4. After payment, Paddle sends webhook to `paddle-webhook`
5. Webhook processes payment and adds credits to user account
6. User is redirected back to VoiceHype with success message

### Vue.js Components

The integration updates:
- **`stores/credits.ts`**: Added Paddle checkout and transaction functions
- **`views/PaymentsView.vue`**: Updated UI for Paddle integration

## API Endpoints

### Create Checkout
```typescript
POST /functions/v1/create-paddle-checkout
Authorization: Bearer <user_token>

{
  "amount": 25,
  "currency": "USD",
  "success_url": "https://voicehype.netlify.app/payments?success=true",
  "cancel_url": "https://voicehype.netlify.app/payments?cancelled=true"
}
```

### Get Transactions
```typescript
GET /functions/v1/get-paddle-transactions?limit=10&offset=0
Authorization: Bearer <user_token>
```

## Security Features

- **Webhook Signature Verification**: All webhooks are verified using HMAC-SHA256
- **Row Level Security**: Database policies ensure users only see their own data
- **Service Role Functions**: Payment processing uses service role for security
- **Environment-based Verification**: Signature verification enabled in production

## Testing

### Sandbox Testing
1. Use Paddle sandbox environment
2. Test with sandbox API keys
3. Use test card numbers provided by Paddle

### Production Deployment
1. Switch to production API keys
2. Update webhook URLs
3. Enable signature verification
4. Test with real payment methods

## Monitoring

### Webhook Logs
Monitor webhook processing in the `paddle.webhooks` table:

```sql
SELECT 
  event_type,
  processed,
  processing_error,
  created_at
FROM paddle.webhooks
ORDER BY created_at DESC;
```

### Transaction Status
Check transaction processing:

```sql
SELECT 
  paddle_transaction_id,
  status,
  amount,
  credit_amount,
  created_at
FROM paddle.transactions
WHERE status = 'completed'
ORDER BY created_at DESC;
```

## Troubleshooting

### Common Issues

1. **Webhook Not Received**
   - Check webhook URL configuration in Paddle
   - Verify Supabase function is deployed
   - Check function logs

2. **Credits Not Added**
   - Check webhook processing logs
   - Verify transaction status in database
   - Check for processing errors

3. **Checkout Creation Fails**
   - Verify API keys are correct
   - Check amount is within $5-$95 range
   - Verify product ID exists in Paddle

### Debug Commands

```sql
-- Check recent webhook events
SELECT * FROM paddle.webhooks ORDER BY created_at DESC LIMIT 10;

-- Check failed transactions
SELECT * FROM paddle.transactions WHERE status != 'completed';

-- Check user credits
SELECT * FROM public.credits WHERE user_id = 'user-uuid-here';
```

## Future Enhancements

- **Subscription Support**: Monthly/yearly plans
- **Refund Handling**: Automatic credit deduction for refunds
- **Usage Analytics**: Detailed spending analytics
- **Enterprise Features**: Volume discounts and custom pricing
