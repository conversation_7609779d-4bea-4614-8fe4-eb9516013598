# Migration Guide: Transitioning from OpenAI's <PERSON>hisper to Lemon Fox Whisper v3

## Overview

This guide outlines the steps required to migrate the VoiceHype extension from using OpenAI's Whisper model to Lemon Fox's Whisper v3 model. This migration is being done to remove dependency on OpenAI services while providing a high-quality, cost-effective alternative transcription service.

> **Note**: All example files referenced in this guide are provided for planning and inspiration purposes only. During implementation, you should refer to these files but adapt them to your specific codebase.

## Reference Implementation Files

For inspiration, the following example files have been created:

1. **Database Migration**:
   - `supabase/migrations/20250501_001_add_lemonfox_whisper/up.sql`
   - `supabase/migrations/20250501_001_add_lemonfox_whisper/down.sql`

2. **Shared Lemon Fox API Implementation**:
   - `supabase/functions/_shared/lemonfox.ts`

3. **Updated UI Components**:
   - `extension/webview-ui/src/components/ModelsSelector.tsx`

## Steps for Migration

### 1. Update Service Pricing Table

Add Lemon Fox's Whisper v3 to the `service_pricing` table in the database by creating a migration file with the following content:

```sql
-- Add <PERSON>'s Whisper v3 to the service_pricing table
INSERT INTO public.service_pricing (service, model, cost_per_unit, unit, is_active, profit_margin)
VALUES ('lemonfox', 'whisper-v3', 0.0003, 'second', true, 0.3);

-- Update the database to track the migration from OpenAI Whisper to Lemon Fox
COMMENT ON TABLE public.service_pricing IS 'Pricing for various AI services including the new Lemon Fox Whisper v3 model';
```

### 2. Update Transcribe Edge Function

The main changes will be in the `supabase/functions/transcribe/index.ts` file:

1. Add Lemon Fox to supported services:

```typescript
const SUPPORTED_SERVICES = ['openai', 'assemblyai', 'lemonfox'] as const;
type TranscriptionService = typeof SUPPORTED_SERVICES[number];

const SUPPORTED_MODELS = {
  openai: ['whisper-1', 'gpt-4o-mini-transcribe', 'gpt-4o-transcribe'] as const,
  assemblyai: ['best', 'nano'] as const,
  lemonfox: ['whisper-v3'] as const
} as const;

type OpenAIModel = typeof SUPPORTED_MODELS['openai'][number];
type AssemblyAIModel = typeof SUPPORTED_MODELS['assemblyai'][number];
type LemonFoxModel = typeof SUPPORTED_MODELS['lemonfox'][number];
type SupportedModel = OpenAIModel | AssemblyAIModel | LemonFoxModel;
```

2. Add environment variable for Lemon Fox API key:

```typescript
// Get service API keys from environment
const ASSEMBLYAI_API_KEY = Deno.env.get('ASSEMBLYAI_API_KEY') || '';
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY') || '';
const LEMONFOX_API_KEY = Deno.env.get('LEMONFOX_API_KEY') || '';

if (!ASSEMBLYAI_API_KEY || !OPENAI_API_KEY || !LEMONFOX_API_KEY) {
  throw new Error('Missing required service API keys');
}
```

3. Create a shared module for Lemon Fox functionality:

It's recommended to create a shared module in `supabase/functions/_shared/lemonfox.ts` that exports the necessary types and functions for Lemon Fox transcription, which can be imported and used in the transcribe function.

4. Update the main transcription handler to support Lemon Fox:

```typescript
// Import the Lemon Fox functions
import { transcribeWithLemonFox, LemonFoxResult } from '../_shared/lemonfox.ts';

// Later in the transcribe function
if (service === 'lemonfox') {
  result = await transcribeWithLemonFox(
    audioData,
    LEMONFOX_API_KEY,
    model as LemonFoxModel,
    language,
    audioDurationSeconds
  );
}
```

### 3. Replace All Instances of "whisper-1"

A crucial step in the migration is to replace all occurrences of "whisper-1" throughout the codebase with "whisper-v3" and update the service from "openai" to "lemonfox". This can be done systematically using VS Code's search and replace functionality:

1. Open VS Code's search panel (Ctrl+Shift+F)
2. Search for `"whisper-1"` with quotes to find exact matches
3. Review all occurrences to ensure they are related to the model name
4. Replace with `"whisper-v3"` where appropriate
5. Similarly, search for instances where the service is set to "openai" when using Whisper
6. Replace these with "lemonfox" where appropriate

Key files to check:
- `extension/webview-ui/src/App.tsx` - Initial model selection
- `extension/src/utils/transcriptionDefaults.ts` - Default settings
- `extension/src/services/transcriptionService.ts` - Service implementations
- `supabase/functions/transcribe/index.ts` - Edge function implementation

### 4. Model-Based Service Selection

According to the requirement, we should modify the UI to select services based on models rather than service providers:

1. Create a new `ModelsSelector.tsx` component that combines all models from different services into a single selection dropdown. The component should:
   - Display model names without explicitly mentioning the service
   - Store both the model ID and service ID for API calls
   - Handle selection changes and update both model and service state

2. Update `App.tsx` to:
   - Replace separate service and model selector components with the new combined selector
   - Update state management to handle the new selection approach
   - Modify API calls to include the correct model and service IDs

### 5. Update Supabase Environment Variables

Add the Lemon Fox API key to your Supabase project's environment variables:

```bash
supabase secrets set LEMONFOX_API_KEY=your_api_key_here
```

## Migration Testing Checklist

- [ ] Add Lemon Fox service pricing to the database
- [ ] Update the transcribe edge function to support Lemon Fox
- [ ] Create shared Lemon Fox implementation module
- [ ] Replace all instances of "whisper-1" with "whisper-v3"
- [ ] Replace all service references from "openai" to "lemonfox" for Whisper transcription
- [ ] Modify UI components to use model-based selection
- [ ] Add Lemon Fox API key to Supabase environment
- [ ] Test transcription with the new service
- [ ] Verify billing functionality works correctly
- [ ] Update documentation to reflect changes

## Implementation Approach

When implementing this migration, it's recommended to:

1. Start with the database changes
2. Create the shared Lemon Fox implementation
3. Update the transcribe edge function
4. Create and test the new UI component
5. Perform systematic search and replace for "whisper-1" and service references
6. Finally, integrate the UI component into the main application

This approach allows for incremental testing at each step of the process.

## Rollback Plan

If issues are encountered during the migration:

1. Revert the database changes using the down migration
2. Restore the original transcribe edge function
3. Revert UI component changes
4. Continue using the current OpenAI and AssemblyAI services

## Future Migration Plans

### Migrating to OpenRouter LLM Models

After successfully migrating from OpenAI's Whisper to Lemon Fox, our next phase will be to migrate other LLM models to OpenRouter. Here's an outline of the planned models:

#### Llama Models
**Current Models**:
- Llama 3 8B: General-purpose model with 8 billion parameters
- Llama 3 70B: High-performance model with 70 billion parameters

**New Llama 4 Models**:
- Llama 4 Scout: 17B active parameters with 16 experts, fits on a single NVIDIA H100 GPU, with 10M token context window
- Llama 4 Maverick: 17B active parameters with 128 experts (400B total parameters), outperforms many larger models
- Llama 4 Behemoth: 288B active parameters (coming soon), Meta's most powerful model to date

These new Llama 4 models are natively multimodal, meaning they can handle both text and image inputs in a unified way, making them excellent choices for applications requiring visual understanding.

#### DeepSeek Models (2 models)
- DeepSeek Coder
- DeepSeek Chat

#### Anthropic Claude Models (4 models)
- Claude 3 Opus
- Claude 3 Sonnet
- Claude 3.5 Sonnet
- Claude 3 Haiku

#### Mistral Models (2 models)
- Mistral Large
- Mistral Small

#### Open Source Models (1 model)
- OpenDevin for code assistance

### Implementation Strategy for LLM Migration

This future migration will follow a similar approach to the current Whisper migration, focusing on:

1. **Database Updates**: Adding the models to the service_pricing table with appropriate pricing
2. **API Integration**: Creating OpenRouter API integration for accessing these models
3. **UI Enhancements**: Updating the UI to show these models without explicitly mentioning the service provider
4. **Billing Integration**: Implementing proper cost tracking and billing for each model

### Benefits of the Llama 4 Models

The new Llama 4 models offer several advantages:

1. **Mixture of Experts (MoE) Architecture**: More efficient compute usage as only a fraction of parameters are activated per token
2. **Multimodal Capabilities**: Native support for image and text processing
3. **Extended Context Windows**: Up to 10 million tokens with Llama 4 Scout
4. **Competitive Performance**: Benchmarks show they outperform many larger models from other providers
5. **Cost-Effectiveness**: High performance-to-cost ratio

By completing both migrations, we will fully eliminate our dependency on OpenAI services while offering an expanded and diverse set of high-quality models to our users. 