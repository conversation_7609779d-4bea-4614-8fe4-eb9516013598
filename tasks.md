Website and supabase:
- Model names 
- Two-factor sign in
- Github sign in
- Google sign in
- Apple sign in
- Remove unnecessary fields from pages such as settings
- Smooth transitions between pages 
- Animations / transitions (loading to result, etc.)
- Only two pricings for now :: Credits (prepaid) and PAYG (card)
- Text too light in the sidebar
- Shellroute for sidebar
- Credits should have expiry date and should be deleted once empty (when doing a ceil results in 0)
- PAYG payment check should be more sophistacated; it should have three states: null, paid, and unpaid. If unpaid, we can restrict the user from using voicehype until the payment is made.
- Credits can be refunded within 10 days of purchase
- Custom subscriptions can be made and bought
- Fix input tokens count by making it extremely precise
- Remove subscription plans from the subscriptions page. Current plans could be:  free plan, credits, and PAYG. 
- Need to make sure our website is responsive on mobile devices and tablets.
- Show random colored perlin noise in the profile picture circle. It should be random based on current time. 
- Show usage analytics in graphs, diagrams, charts, tables, etc.
- Limit PAYG history to 6 months.
- Integrate payments using Paddle
- API key id in usage_history is null. Edit the edge functions and the finalize_usage function to also include the API key id.
- Remove usage of subscription showing from the dashboard if user has no subscription. 
- Don't show the free trial as a plan unless the user has subscribed to it.
- Show input and output tokens on the Usage page
- Browser extension, software using Flutter
- Cost should be shown in full decimal places on the dashboard
- Host edge functions on DigitalOcean
- Slice the audio and concurrently transcribe those chunks inside the edge function. Do it real-time to make it even faster.
- If the user's credits are limited and the audio duration exceeds their available credits, we should slice the audio to match the number of credits the user has. Instead of denying the transcription entirely, we will transcribe only the portion of the audio that corresponds to the user's available credits. This process should be implemented when the user's credits are critically low.
- Count tokens accurately
```
  "usage": {
    "input_tokens": 36,
    "input_tokens_details": {
      "cached_tokens": 0
    },
    "output_tokens": 87,
    "output_tokens_details": {
      "reasoning_tokens": 0
    },
    "total_tokens": 123
  }
```
- Create a table for storing negative balances. Negative balances may arise due to over-usage of optimization. 
Token prediction has to be done like this: 
1. Calculate cost of input tokens
2. Deduct that from available credits
3. Now that result is the amount of credits available.

    For now, we can ignore negative balances since they won't be that many and it is very rare that they may ever happen, Inshaa Allah.

- Implement `tiktoken` for accurate token estimation
- Deploy the real-time transcription function
- Monthly limit for PAYG usage
- 20 mins MAX for real-time and OR(20 MINS, 25MB) for normal


Extension: 
- Remove replicate models as we are no longer using models from replicate (also from settings)
- Resolve state management issues
- Create a beautiful extension pane
- Have only two openai models
- Change language when switching between models or service
- It is pasting two times both in normal and optimize modes
- Analyze using sonnet.thinking and deepseek.r1 models on how we can make the extension better for users, so much so that they start loving it.
- Add realtime transcription using AssemblyAI
- Add realtime translation using WhisperLive
- Port maximum features from both platforms to the extension
- Second person, third person, etc., tone, ...
- Being able to reference command to LLM in the same transcript by calling "VoiceHype, I want you to format this into a prompt and ..."
- Pause and resume transcription functionality 
- Stop icon isn't shown
- Presets
- Comment out snackbar logs from Microphone.ts
- Fix "AI-optimized mode using undefined"
- Add "Auto" to languages to AssemblyAI
- Add more interactivity and logging using snackbars, loading indicators, etc.
- Store transcription and optimization history items in an SQL database. Additionally, integrate the "translate to English" feature into the edge function and the extension.

Cron jobs:
- At the end of the month, process payments for PAYG usages. 
- Every day, check which subscriptions have expired then delete them and their quotas.
- Every day, check which credits have expired then delete them.
- Every day, check which accounts have gone unused for more than six months. Delete them.
- Delete PAYG usages longer than six months.

**Inshaa Allah**

---

- Auto-detect for both Whisper and AssemblyAI
- Website.finalize
- Use compressed m4a format using ffmpeg, Inshaa Allah
- Calculate duration of successful audio chunks using the formula we have, Alhamdulillah

**Inshaa Allah**