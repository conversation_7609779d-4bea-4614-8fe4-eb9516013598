CREATE OR REPLACE FUNCTION "public"."check_usage_allowance"(
    "p_user_id" "uuid",
    "p_service" "text",
    "p_model" "text",
    "p_amount" numeric,
    "p_api_key_id" "uuid",
    "p_is_input_only" boolean DEFAULT false
) RETURNS TABLE(
    "can_use" boolean,
    "pricing_model" "text",
    "cost" numeric,
    "max_output_tokens" "text",
    "error_code" "text"
)
LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
DECLARE
    subscription_available BOOLEAN;
    credit_balance DECIMAL(18,9);
    service_cost DECIMAL(18,9);
    cost_per_unit DECIMAL(18,9);
    payg_allowed BOOLEAN;
    input_cost_per_unit DECIMAL(18,9);
    output_cost_per_unit DECIMAL(18,9);
    max_tokens INTEGER;
    has_unpaid_previous_month BOOLEAN;
    is_token_based BOOLEAN;
    avg_tokens_per_minute INTEGER;
    avg_token_cost DECIMAL(18,9);
    estimated_minutes INTEGER;
    pricing_unit TEXT;
    remaining_quota DECIMAL(18,9);
    remaining_allowance DECIMAL(18,9);
BEGIN
    -- Get information about the model's pricing unit
    SELECT sp.unit INTO pricing_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;
    
    -- Check if this is a token-based model
    is_token_based := pricing_unit = 'token-based';
    
    -- Get cost per unit first for standard pricing
    SELECT sp.cost_per_unit INTO cost_per_unit
    FROM public.service_pricing sp
    WHERE sp.service = p_service
    AND sp.model = p_model
    AND sp.is_active = TRUE;

    -- Calculate standard service cost with proper decimal precision
    IF cost_per_unit IS NOT NULL AND NOT is_token_based THEN
        service_cost := cost_per_unit * p_amount;
    END IF;

    -- Check subscription quota first
    SELECT EXISTS (
        SELECT 1 FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW()
    ) INTO subscription_available;

    -- If subscription is available, check remaining quota
    IF subscription_available THEN
        -- Get remaining quota for this service
        SELECT (q.total_amount - q.used_amount) INTO remaining_quota
        FROM public.quotas q
        JOIN public.user_subscriptions us ON q.subscription_id = us.id
        WHERE us.user_id = p_user_id
        AND q.service = p_service
        AND us.status = 'active'
        AND q.reset_date > NOW()
        LIMIT 1;

        -- For optimization service with subscription, calculate max tokens
        IF p_service = 'optimization' AND remaining_quota > 0 THEN
            -- Cap at 4096 tokens maximum
            max_tokens := LEAST(FLOOR(remaining_quota), 4096);
            
            RETURN QUERY SELECT 
                TRUE as can_use,
                'subscription'::TEXT as pricing_model,
                service_cost,
                max_tokens::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        -- For other services with subscription, check if quota is sufficient
        ELSIF remaining_quota >= p_amount THEN
            RETURN QUERY SELECT 
                TRUE as can_use,
                'subscription'::TEXT as pricing_model,
                service_cost,
                '4096'::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- Check user's credit balance
    SELECT COALESCE(balance, 0) INTO credit_balance
    FROM public.credits
    WHERE user_id = p_user_id;

    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;
    
    -- Check if user has unpaid balances from previous months
    SELECT EXISTS (
        SELECT 1 
        FROM public.payg_usage 
        WHERE user_id = p_user_id 
        AND payment_status = 'unpaid'
        AND month < date_trunc('month', CURRENT_DATE)
    ) INTO has_unpaid_previous_month;
    
    -- Special handling for token-based transcription models (GPT-4o)
    IF is_token_based AND p_service = 'transcription' THEN
        -- Get token costs for this model
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/input'
        AND sp.is_active = true;
        
        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'transcription'
        AND sp.model = p_model || '/output'
        AND sp.is_active = true;
        
        IF input_cost_per_unit IS NULL OR output_cost_per_unit IS NULL THEN
            RETURN QUERY SELECT 
                FALSE::BOOLEAN as can_use,
                NULL::TEXT as pricing_model,
                0::DECIMAL(18,9) as cost,
                NULL::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
        
        -- Set estimated words per minute based on model
        IF p_model = 'gpt-4o-mini-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSIF p_model = 'gpt-4o-transcribe' THEN
            avg_tokens_per_minute := 200; -- approx. 150 words per minute
        ELSE
            avg_tokens_per_minute := 200; -- default
        END IF;
        
        -- Calculate average token cost
        avg_token_cost := (input_cost_per_unit + output_cost_per_unit) / 2;
        
        -- Calculate how many minutes of audio the user can afford
        estimated_minutes := FLOOR(credit_balance / (avg_tokens_per_minute * avg_token_cost));
        
        -- Always allow usage for credits system (will track negative balances)
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            input_cost_per_unit * avg_tokens_per_minute * p_amount as cost,
            estimated_minutes::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Special handling for optimization service (LLM calls)
    IF p_service = 'optimization' THEN
        -- Try to get separate input/output pricing
        SELECT sp.cost_per_unit INTO input_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/input'
        AND sp.is_active = TRUE;

        SELECT sp.cost_per_unit INTO output_cost_per_unit
        FROM public.service_pricing sp
        WHERE sp.service = 'optimization'
        AND sp.model = p_model || '/output'
        AND sp.is_active = TRUE;

        -- If we have separate input/output pricing
        IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
            -- Calculate input tokens cost
            service_cost := input_cost_per_unit * p_amount;
            
            -- Calculate how many output tokens they can afford
            -- Cap at 4096 tokens maximum
            max_tokens := LEAST(FLOOR(credit_balance / output_cost_per_unit), 4096);
            
            -- Always allow usage for credits system (will track negative balances)
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                max_tokens::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        ELSE
            -- If we don't have separate pricing, use the combined pricing
            -- Calculate max tokens they can afford (input + output)
            -- Cap at 4096 tokens maximum
            max_tokens := LEAST(FLOOR(credit_balance / cost_per_unit), 4096);
            
            -- Always allow usage for credits system (will track negative balances)
            RETURN QUERY SELECT 
                TRUE as can_use,
                'credits'::TEXT as pricing_model,
                service_cost,
                max_tokens::TEXT as max_output_tokens,
                NULL::TEXT as error_code;
            RETURN;
        END IF;
    END IF;

    -- For non-optimization services and non-token-based transcription, use the original logic
    -- Always allow usage for credits system (will track negative balances)
    IF NOT is_token_based THEN
        RETURN QUERY SELECT 
            TRUE as can_use,
            'credits'::TEXT as pricing_model,
            service_cost,
            NULL::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- Check if PAYG is allowed for this API key
    SELECT COALESCE(allow_payg, FALSE) INTO payg_allowed
    FROM public.api_keys
    WHERE id = p_api_key_id
    AND is_active = TRUE;

    IF payg_allowed IS NULL THEN
        payg_allowed := FALSE;
    END IF;

    -- Now check for unpaid balances only if we're going to use PAYG
    -- This ensures credits are prioritized over PAYG even if there are unpaid balances
    IF has_unpaid_previous_month THEN
        RETURN QUERY SELECT 
            FALSE::BOOLEAN as can_use,
            NULL::TEXT as pricing_model,
            0::DECIMAL(18,9) as cost,
            NULL::TEXT as max_output_tokens,
            'unpaid_balance'::TEXT as error_code;
        RETURN;
    END IF;

    -- Fallback to PAYG if allowed, user has stripe customer id, and no unpaid balances
    IF payg_allowed 
       AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = p_user_id
        AND stripe_customer_id IS NOT NULL
    ) THEN
        -- Get user's credit allowance from profiles table
        SELECT credit_allowance INTO remaining_allowance
        FROM public.profiles
        WHERE id = p_user_id;
        
        -- For PAYG optimization, calculate max tokens based on remaining allowance
        IF p_service = 'optimization' THEN
            -- Get remaining PAYG allowance for this month
            SELECT COALESCE(remaining_allowance - SUM(total_amount), remaining_allowance) INTO remaining_allowance
            FROM public.payg_usage
            WHERE user_id = p_user_id
            AND month = date_trunc('month', CURRENT_DATE);
            
            -- Get token costs for this model
            SELECT sp.cost_per_unit INTO input_cost_per_unit
            FROM public.service_pricing sp
            WHERE sp.service = 'optimization'
            AND sp.model = p_model || '/input'
            AND sp.is_active = TRUE;

            SELECT sp.cost_per_unit INTO output_cost_per_unit
            FROM public.service_pricing sp
            WHERE sp.service = 'optimization'
            AND sp.model = p_model || '/output'
            AND sp.is_active = TRUE;

            -- If we have separate input/output pricing
            IF input_cost_per_unit IS NOT NULL AND output_cost_per_unit IS NOT NULL THEN
                -- Calculate input tokens cost
                service_cost := input_cost_per_unit * p_amount;
                
                -- Calculate how many output tokens they can afford with remaining allowance
                -- First subtract the cost of input tokens
                IF remaining_allowance > service_cost THEN
                    -- Remaining allowance after input tokens
                    remaining_allowance := remaining_allowance - service_cost;
                    
                    -- Calculate max output tokens they can afford
                    max_tokens := FLOOR(remaining_allowance / output_cost_per_unit);
                    
                    -- Cap at a reasonable maximum (4096 is common for many models)
                    max_tokens := LEAST(max_tokens, 4096);
                    
                    RETURN QUERY SELECT 
                        TRUE::BOOLEAN as can_use,
                        'payg'::TEXT as pricing_model,
                        service_cost,
                        max_tokens::TEXT as max_output_tokens,
                        NULL::TEXT as error_code;
                    RETURN;
                END IF;
            ELSE
                -- If we don't have separate pricing, use the combined pricing
                -- Calculate max tokens they can afford (input + output)
                max_tokens := LEAST(FLOOR(remaining_allowance / cost_per_unit), 4096);
                
                RETURN QUERY SELECT 
                    TRUE::BOOLEAN as can_use,
                    'payg'::TEXT as pricing_model,
                    service_cost,
                    max_tokens::TEXT as max_output_tokens,
                    NULL::TEXT as error_code;
                RETURN;
            END IF;
        END IF;
        
        -- For non-optimization PAYG services
        RETURN QUERY SELECT 
            TRUE::BOOLEAN as can_use,
            'payg'::TEXT as pricing_model,
            service_cost,
            '4096'::TEXT as max_output_tokens,
            NULL::TEXT as error_code;
        RETURN;
    END IF;

    -- If we get here, no payment method is available
    RETURN QUERY SELECT 
        FALSE::BOOLEAN as can_use,
        NULL::TEXT as pricing_model,
        service_cost,
        NULL::TEXT as max_output_tokens,
        'insufficient_credits'::TEXT as error_code;
END;
$$;
