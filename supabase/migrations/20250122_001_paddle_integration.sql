-- Paddle Integration Schema
-- This migration creates the necessary tables and functions for Paddle payment integration

-- <PERSON>reate paddle schema
CREATE SCHEMA IF NOT EXISTS paddle;

-- Grant permissions on paddle schema
GRANT USAGE ON SCHEMA paddle TO postgres, anon, authenticated, service_role;

-- Paddle customers table
CREATE TABLE IF NOT EXISTS paddle.customers (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_customer_id TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    name TEXT,
    country_code TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Paddle products table (for the main VoiceHype service)
CREATE TABLE IF NOT EXISTS paddle.products (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    paddle_product_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('credits', 'subscription')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Paddle transactions table
CREATE TABLE IF NOT EXISTS paddle.transactions (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_transaction_id TEXT UNIQUE NOT NULL,
    paddle_customer_id TEXT NOT NULL,
    product_id UUID REFERENCES paddle.products(id),
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
    amount DECIMAL(18,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    credit_amount DECIMAL(18,9), -- Credits purchased (for credit packages)
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('credit_purchase', 'payg_invoice', 'refund')),
    paddle_receipt_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Paddle subscriptions table (for recurring payments if needed)
CREATE TABLE IF NOT EXISTS paddle.subscriptions (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    paddle_subscription_id TEXT UNIQUE NOT NULL,
    paddle_customer_id TEXT NOT NULL,
    product_id UUID REFERENCES paddle.products(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'paused', 'cancelled', 'past_due')),
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Paddle webhooks table (for logging and debugging)
CREATE TABLE IF NOT EXISTS paddle.webhooks (
    id UUID DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    paddle_event_id TEXT UNIQUE NOT NULL,
    event_type TEXT NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    raw_payload JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_paddle_customers_user_id ON paddle.customers(user_id);
CREATE INDEX IF NOT EXISTS idx_paddle_customers_paddle_id ON paddle.customers(paddle_customer_id);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_user_id ON paddle.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_paddle_id ON paddle.transactions(paddle_transaction_id);
CREATE INDEX IF NOT EXISTS idx_paddle_transactions_status ON paddle.transactions(status);
CREATE INDEX IF NOT EXISTS idx_paddle_subscriptions_user_id ON paddle.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_paddle_subscriptions_paddle_id ON paddle.subscriptions(paddle_subscription_id);
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_event_id ON paddle.webhooks(paddle_event_id);
CREATE INDEX IF NOT EXISTS idx_paddle_webhooks_processed ON paddle.webhooks(processed);

-- Add updated_at trigger for tables
CREATE OR REPLACE FUNCTION paddle.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_paddle_customers_updated_at BEFORE UPDATE ON paddle.customers FOR EACH ROW EXECUTE FUNCTION paddle.update_updated_at_column();
CREATE TRIGGER update_paddle_products_updated_at BEFORE UPDATE ON paddle.products FOR EACH ROW EXECUTE FUNCTION paddle.update_updated_at_column();
CREATE TRIGGER update_paddle_transactions_updated_at BEFORE UPDATE ON paddle.transactions FOR EACH ROW EXECUTE FUNCTION paddle.update_updated_at_column();
CREATE TRIGGER update_paddle_subscriptions_updated_at BEFORE UPDATE ON paddle.subscriptions FOR EACH ROW EXECUTE FUNCTION paddle.update_updated_at_column();

-- Function to create or get Paddle customer
CREATE OR REPLACE FUNCTION paddle.create_or_get_customer(
    p_user_id UUID,
    p_paddle_customer_id TEXT,
    p_email TEXT,
    p_name TEXT DEFAULT NULL,
    p_country_code TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_customer_id UUID;
BEGIN
    -- Try to find existing customer
    SELECT id INTO v_customer_id
    FROM paddle.customers
    WHERE user_id = p_user_id OR paddle_customer_id = p_paddle_customer_id;

    -- If not found, create new customer
    IF v_customer_id IS NULL THEN
        INSERT INTO paddle.customers (
            user_id,
            paddle_customer_id,
            email,
            name,
            country_code
        ) VALUES (
            p_user_id,
            p_paddle_customer_id,
            p_email,
            p_name,
            p_country_code
        ) RETURNING id INTO v_customer_id;
    ELSE
        -- Update existing customer with latest info
        UPDATE paddle.customers
        SET paddle_customer_id = p_paddle_customer_id,
            email = p_email,
            name = COALESCE(p_name, name),
            country_code = COALESCE(p_country_code, country_code),
            updated_at = NOW()
        WHERE id = v_customer_id;
    END IF;

    RETURN v_customer_id;
END;
$$;

-- Function to process completed transaction and add credits
CREATE OR REPLACE FUNCTION paddle.process_completed_transaction(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_product_id TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_customer_id UUID;
    v_product_id UUID;
    v_credit_amount DECIMAL(18,9);
    v_transaction_id UUID;
    v_existing_transaction UUID;
BEGIN
    -- Check if transaction already exists
    SELECT id INTO v_existing_transaction
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_existing_transaction IS NOT NULL THEN
        -- Transaction already processed
        RETURN TRUE;
    END IF;

    -- Get customer and user info
    SELECT user_id INTO v_user_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Customer not found for paddle_customer_id: %', p_paddle_customer_id;
    END IF;

    -- Get product info if product_id provided (for reference only)
    IF p_product_id IS NOT NULL THEN
        SELECT id INTO v_product_id
        FROM paddle.products
        WHERE paddle_product_id = p_product_id AND is_active = TRUE;

        IF v_product_id IS NULL THEN
            RAISE EXCEPTION 'Product not found for paddle_product_id: %', p_product_id;
        END IF;
    END IF;

    -- For VoiceHype, $1 = 1 credit (flexible credit purchasing)
    v_credit_amount := p_amount;

    -- Create transaction record
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        product_id,
        status,
        amount,
        currency,
        credit_amount,
        transaction_type,
        paddle_receipt_url,
        metadata
    ) VALUES (
        v_user_id,
        p_paddle_transaction_id,
        p_paddle_customer_id,
        v_product_id,
        'completed',
        p_amount,
        p_currency,
        v_credit_amount,
        'credit_purchase',
        p_receipt_url,
        p_metadata
    ) RETURNING id INTO v_transaction_id;

    -- Add credits to user's account
    INSERT INTO public.credits (user_id, balance, currency, created_at, updated_at)
    VALUES (v_user_id, v_credit_amount, p_currency, NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        balance = credits.balance + v_credit_amount,
        updated_at = NOW();

    RETURN TRUE;
END;
$$;

-- Row Level Security Policies
ALTER TABLE paddle.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE paddle.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE paddle.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE paddle.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE paddle.webhooks ENABLE ROW LEVEL SECURITY;

-- Customers policies
CREATE POLICY "Users can view their own paddle customers" ON paddle.customers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage paddle customers" ON paddle.customers
    FOR ALL USING (auth.role() = 'service_role');

-- Products policies (public read for active products)
CREATE POLICY "Anyone can view active paddle products" ON paddle.products
    FOR SELECT USING (is_active = TRUE);

CREATE POLICY "Service role can manage paddle products" ON paddle.products
    FOR ALL USING (auth.role() = 'service_role');

-- Transactions policies
CREATE POLICY "Users can view their own paddle transactions" ON paddle.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage paddle transactions" ON paddle.transactions
    FOR ALL USING (auth.role() = 'service_role');

-- Subscriptions policies
CREATE POLICY "Users can view their own paddle subscriptions" ON paddle.subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage paddle subscriptions" ON paddle.subscriptions
    FOR ALL USING (auth.role() = 'service_role');

-- Webhooks policies (service role only)
CREATE POLICY "Service role can manage paddle webhooks" ON paddle.webhooks
    FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions on tables
GRANT ALL ON ALL TABLES IN SCHEMA paddle TO service_role;
GRANT SELECT ON paddle.products TO anon, authenticated;
GRANT SELECT ON paddle.customers TO authenticated;
GRANT SELECT ON paddle.transactions TO authenticated;
GRANT SELECT ON paddle.subscriptions TO authenticated;

-- Grant permissions on functions
GRANT EXECUTE ON FUNCTION paddle.create_or_get_customer TO service_role;
GRANT EXECUTE ON FUNCTION paddle.process_completed_transaction TO service_role;
GRANT EXECUTE ON FUNCTION paddle.create_payg_invoice TO service_role;
GRANT EXECUTE ON FUNCTION paddle.process_payg_payment TO service_role;
GRANT EXECUTE ON FUNCTION paddle.get_unpaid_payg_balances TO service_role;

-- Function to create PAYG invoice in Paddle
CREATE OR REPLACE FUNCTION paddle.create_payg_invoice(
    p_user_id UUID,
    p_month DATE,
    p_amount DECIMAL(18,9)
) RETURNS UUID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_customer_id UUID;
    v_paddle_customer_id TEXT;
    v_transaction_id UUID;
BEGIN
    -- Get Paddle customer ID
    SELECT id, paddle_customer_id INTO v_customer_id, v_paddle_customer_id
    FROM paddle.customers
    WHERE user_id = p_user_id;

    IF v_customer_id IS NULL THEN
        RAISE EXCEPTION 'No Paddle customer found for user: %', p_user_id;
    END IF;

    -- Create transaction record for PAYG invoice
    INSERT INTO paddle.transactions (
        user_id,
        paddle_transaction_id,
        paddle_customer_id,
        status,
        amount,
        currency,
        transaction_type,
        metadata
    ) VALUES (
        p_user_id,
        'payg_' || p_user_id || '_' || to_char(p_month, 'YYYY_MM'),
        v_paddle_customer_id,
        'pending',
        p_amount,
        'USD',
        'payg_invoice',
        jsonb_build_object(
            'month', p_month,
            'invoice_type', 'payg_monthly',
            'created_at', NOW()
        )
    ) RETURNING id INTO v_transaction_id;

    RETURN v_transaction_id;
END;
$$;

-- Function to process PAYG payment completion
CREATE OR REPLACE FUNCTION paddle.process_payg_payment(
    p_paddle_transaction_id TEXT,
    p_paddle_customer_id TEXT,
    p_amount DECIMAL(18,2),
    p_currency TEXT,
    p_receipt_url TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_month DATE;
    v_payg_id UUID;
BEGIN
    -- Get user ID from customer
    SELECT user_id INTO v_user_id
    FROM paddle.customers
    WHERE paddle_customer_id = p_paddle_customer_id;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Customer not found for paddle_customer_id: %', p_paddle_customer_id;
    END IF;

    -- Extract month from transaction metadata
    SELECT (metadata->>'month')::DATE INTO v_month
    FROM paddle.transactions
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    IF v_month IS NULL THEN
        RAISE EXCEPTION 'Month not found in transaction metadata for: %', p_paddle_transaction_id;
    END IF;

    -- Update transaction status
    UPDATE paddle.transactions
    SET status = 'completed',
        paddle_receipt_url = p_receipt_url,
        metadata = metadata || p_metadata,
        updated_at = NOW()
    WHERE paddle_transaction_id = p_paddle_transaction_id;

    -- Mark PAYG usage as paid
    UPDATE public.payg_usage
    SET payment_status = 'paid',
        payment_metadata = jsonb_build_object(
            'paddle_transaction_id', p_paddle_transaction_id,
            'paddle_receipt_url', p_receipt_url,
            'paid_at', NOW()
        )
    WHERE user_id = v_user_id
    AND month = v_month;

    RETURN TRUE;
END;
$$;

-- Function to get unpaid PAYG balances for billing
CREATE OR REPLACE FUNCTION paddle.get_unpaid_payg_balances()
RETURNS TABLE (
    user_id UUID,
    month DATE,
    total_amount DECIMAL(18,9),
    user_email TEXT,
    user_name TEXT
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        pu.user_id,
        pu.month,
        pu.total_amount,
        p.email,
        p.full_name
    FROM public.payg_usage pu
    JOIN public.profiles p ON pu.user_id = p.id
    WHERE pu.payment_status = 'pending'
    AND pu.month < date_trunc('month', CURRENT_DATE)
    AND pu.total_amount > 0
    ORDER BY pu.month ASC, pu.user_id;
END;
$$;

-- Insert the main VoiceHype products (for reference in Paddle)
INSERT INTO paddle.products (paddle_product_id, name, description, type) VALUES
('prod_voicehype_credits', 'VoiceHype Credits', 'Flexible credit purchasing for VoiceHype services', 'credits'),
('prod_voicehype_payg', 'VoiceHype Pay-as-you-go', 'Monthly billing for VoiceHype usage', 'subscription')
ON CONFLICT (paddle_product_id) DO NOTHING;
