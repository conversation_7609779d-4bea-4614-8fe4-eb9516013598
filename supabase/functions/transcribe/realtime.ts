// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from '@supabase/supabase-js';
import {
    createErrorResponse,
    createSuccessResponse,
    ErrorCode
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
    env: {
        get(key: string): string | undefined;
    };
    upgradeWebSocket(req: Request): { socket: WebSocket; response: Response };
};

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get API keys from environment
const ASSEMBLYAI_API_KEY = Deno.env.get('ASSEMBLYAI_API_KEY') || '';
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY') || '';

if (!ASSEMBLYAI_API_KEY || !OPENAI_API_KEY) {
    throw new Error('Missing required service API keys');
}

// Constants
const MAX_SESSION_DURATION_MINUTES = 20; // Maximum session duration in minutes

// Supported services and models
const SUPPORTED_SERVICES = ['openai', 'assemblyai'] as const;
type TranscriptionService = typeof SUPPORTED_SERVICES[number];

const SUPPORTED_MODELS = {
    openai: ['whisper-1', 'gpt-4o-mini-transcribe', 'gpt-4o-transcribe'] as const,
    assemblyai: ['best', 'nano'] as const
} as const;

type OpenAIModel = typeof SUPPORTED_MODELS['openai'][number];
type AssemblyAIModel = typeof SUPPORTED_MODELS['assemblyai'][number];
type SupportedModel = OpenAIModel | AssemblyAIModel;

// Helper function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
    const obj: Record<string, string> = {};
    headers.forEach((value, key) => {
        obj[key] = value;
    });
    return obj;
}

// Function to generate a temporary token for AssemblyAI real-time transcription
async function generateAssemblyAITemporaryToken(expiresIn: number = 480): Promise<string> {
    console.log('Generating temporary token for AssemblyAI');

    try {
        const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
            method: 'POST',
            headers: {
                'Authorization': ASSEMBLYAI_API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expires_in: expiresIn
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error generating AssemblyAI token:', response.status, errorText);
            throw new Error(`Failed to generate AssemblyAI token: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        console.log('Successfully generated AssemblyAI temporary token');
        return data.token;
    } catch (error) {
        console.error('Error generating AssemblyAI token:', error);
        throw error;
    }
}

// Function to connect to OpenAI's real-time transcription service
async function connectToOpenAI(
    socket: WebSocket,
    userId: string,
    apiKeyId: string,
    model: string,
    language: string,
    sessionId: string,
    availableMinutes: number
): Promise<void> {
    let isConnected = false;
    let openAISocket: WebSocket | null = null;
    let sessionStartTime = Date.now();
    let audioBytesSent = 0;
    let transcriptionReceived = false;
    let sessionTimeout: number | null = null;

    // For token-based billing of GPT-4o models
    let inputTokenCount = 0;
    let outputTokenCount = 0;
    const isGPT4oModel = model.startsWith('gpt-4o');

    // Set a timeout for the maximum session duration (minimum of available minutes or 20 minutes)
    const maxDurationMs = Math.min(availableMinutes, MAX_SESSION_DURATION_MINUTES) * 60 * 1000;

    console.log(`Setting up OpenAI connection for session ${sessionId} with max duration ${maxDurationMs}ms`);

    // Record pending usage
    await supabase
        .from('usage_history')
        .insert({
            user_id: userId,
            api_key_id: apiKeyId,
            service: 'transcription',
            model: model + '-realtime',
            amount: 0, // Will be updated when the session ends
            cost: 0, // Will be updated when the session ends
            pricing_model: 'credits', // Default to credits, will be updated later
            status: 'pending',
            metadata: {
                sessionId,
                startTime: sessionStartTime,
                maxDurationMs,
                isTokenBased: isGPT4oModel
            }
        });

    // OpenAI WebSocket URL for real-time transcription
    const openAIUrl = 'wss://api.openai.com/v1/realtime?intent=transcription';

    // Set up WebSocket with OpenAI
    try {
        openAISocket = new WebSocket(openAIUrl, ['Bearer', OPENAI_API_KEY]);

        // Set up session timeout
        sessionTimeout = setTimeout(() => {
            console.log(`Session ${sessionId} reached maximum duration of ${maxDurationMs}ms`);

            // Send timeout notification to client
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'timeout',
                    message: 'Session reached maximum duration'
                }));
            }

            // Close OpenAI connection if still open
            if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
                openAISocket.close();
            }

            // Close client connection if still open
            if (socket.readyState === WebSocket.OPEN) {
                socket.close();
            }
        }, maxDurationMs);

        openAISocket.onopen = () => {
            console.log(`Connected to OpenAI for session ${sessionId}`);
            isConnected = true;

            // Send configuration to OpenAI
            const configMessage: any = {
                type: "transcription_session.update",
                input_audio_format: "pcm16",
                input_audio_transcription: {
                    model: model,
                    prompt: "",
                    language: language || ""
                },
                turn_detection: {
                    type: "server_vad",
                    threshold: 0.5,
                    prefix_padding_ms: 300,
                    silence_duration_ms: 500,
                },
                input_audio_noise_reduction: {
                    type: "near_field"
                }
            };

            // For GPT-4o models, request token counts
            if (isGPT4oModel) {
                configMessage.include_tokens = true;
            }

            openAISocket?.send(JSON.stringify(configMessage));

            // Notify client that connection is ready
            socket.send(JSON.stringify({
                type: 'connected',
                sessionId: sessionId,
                maxDurationMs: maxDurationMs,
                isTokenBased: isGPT4oModel
            }));
        };

        openAISocket.onmessage = (event) => {
            // Parse and forward transcription results to the client
            try {
                const data = JSON.parse(event.data);

                // Handle different message types from OpenAI
                switch (data.type) {
                    case "item.input_audio_transcription.fragment":
                        transcriptionReceived = true;

                        // Track tokens for GPT-4o models (if provided by OpenAI)
                        if (isGPT4oModel && data.transcription?.tokens) {
                            if (data.transcription.tokens.input) {
                                inputTokenCount += data.transcription.tokens.input;
                            }
                            if (data.transcription.tokens.output) {
                                outputTokenCount += data.transcription.tokens.output;
                            }
                        }

                        socket.send(JSON.stringify({
                            type: 'partial',
                            text: data.transcription?.text || "",
                            timestamp: Date.now(),
                            tokens: data.transcription?.tokens || null
                        }));
                        break;
                    case "item.input_audio_transcription.completed":
                        transcriptionReceived = true;

                        // Track tokens for GPT-4o models (if provided by OpenAI)
                        if (isGPT4oModel && data.transcription?.tokens) {
                            if (data.transcription.tokens.input) {
                                inputTokenCount += data.transcription.tokens.input;
                            }
                            if (data.transcription.tokens.output) {
                                outputTokenCount += data.transcription.tokens.output;
                            }
                        }

                        socket.send(JSON.stringify({
                            type: 'final',
                            text: data.transcription?.text || "",
                            timestamp: Date.now(),
                            tokens: data.transcription?.tokens || null
                        }));
                        break;
                    case "input_audio_buffer.committed":
                        // Audio buffer committed, no action needed
                        break;
                    default:
                        // Forward any other messages as-is
                        socket.send(event.data);
                }
            } catch (error) {
                console.error(`Error parsing OpenAI message for session ${sessionId}:`, error);
            }
        };

        openAISocket.onerror = (event) => {
            console.error(`OpenAI WebSocket error for session ${sessionId}:`, event);
            socket.send(JSON.stringify({
                type: 'error',
                message: 'Error connecting to OpenAI transcription service'
            }));
        };

        openAISocket.onclose = (event) => {
            console.log(`OpenAI WebSocket closed for session ${sessionId}:`, event.code, event.reason);
            isConnected = false;

            // Clear the session timeout
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Notify client that OpenAI connection is closed
            socket.send(JSON.stringify({
                type: 'service_disconnected',
                code: event.code,
                reason: event.reason
            }));

            // Record final session usage with token information for GPT-4o models
            if (isGPT4oModel && (inputTokenCount > 0 || outputTokenCount > 0)) {
                finalizeSession(
                    userId,
                    apiKeyId,
                    model,
                    sessionId,
                    sessionStartTime,
                    audioBytesSent,
                    transcriptionReceived,
                    { input_tokens: inputTokenCount, output_tokens: outputTokenCount },
                    (Date.now() - sessionStartTime) / 1000
                );
            } else {
                finalizeSession(
                    userId,
                    apiKeyId,
                    model,
                    sessionId,
                    sessionStartTime,
                    audioBytesSent,
                    transcriptionReceived,
                    undefined,
                    (Date.now() - sessionStartTime) / 1000
                );
            }
        };

        // Set up message handler for the client socket
        socket.onmessage = (event) => {
            try {
                // Check if the message is binary audio data
                if (event.data instanceof ArrayBuffer) {
                    if (isConnected && openAISocket?.readyState === WebSocket.OPEN) {
                        audioBytesSent += event.data.byteLength;

                        // Format the audio data for OpenAI
                        openAISocket.send(JSON.stringify({
                            type: "input_audio_buffer.append",
                            audio: btoa(String.fromCharCode(...new Uint8Array(event.data)))
                        }));
                    }
                } else {
                    // Handle JSON control messages from client
                    const message = JSON.parse(event.data);

                    if (message.type === 'close') {
                        // Client requested to close the connection
                        if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
                            openAISocket.close();
                        }

                        if (sessionTimeout !== null) {
                            clearTimeout(sessionTimeout);
                            sessionTimeout = null;
                        }

                        socket.close();
                    }
                }
            } catch (error) {
                console.error(`Error handling client message for session ${sessionId}:`, error);
            }
        };

        // Handle client disconnection
        socket.onclose = () => {
            console.log(`Client WebSocket closed for session ${sessionId}`);

            // Clear the session timeout
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Close OpenAI connection if still open
            if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
                openAISocket.close();
            }

            // Record final session usage with token information for GPT-4o models
            if (isGPT4oModel && (inputTokenCount > 0 || outputTokenCount > 0)) {
                finalizeSession(
                    userId,
                    apiKeyId,
                    model,
                    sessionId,
                    sessionStartTime,
                    audioBytesSent,
                    transcriptionReceived,
                    { input_tokens: inputTokenCount, output_tokens: outputTokenCount },
                    (Date.now() - sessionStartTime) / 1000
                );
            } else {
                finalizeSession(
                    userId,
                    apiKeyId,
                    model,
                    sessionId,
                    sessionStartTime,
                    audioBytesSent,
                    transcriptionReceived,
                    undefined,
                    (Date.now() - sessionStartTime) / 1000
                );
            }
        };

        socket.onerror = (event) => {
            console.error(`Client WebSocket error for session ${sessionId}:`, event);
        };
    } catch (error) {
        console.error(`Error setting up OpenAI connection for session ${sessionId}:`, error);

        if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to establish connection with OpenAI'
            }));
            socket.close();
        }
    }

    // After finalizing the session, send the duration information to the client
    if (socket.readyState === WebSocket.OPEN) {
        // Calculate duration in seconds
        const durationSeconds = (Date.now() - sessionStartTime) / 1000;
        
        // Send duration information to client
        socket.send(JSON.stringify({
            type: 'duration',
            duration: durationSeconds,
            processingComplete: true,
            sessionId: sessionId
        }));
        
        // Then send the finalization confirmation
        socket.send(JSON.stringify({
            type: 'finalized',
            message: 'Session finalized successfully',
            sessionId: sessionId
        }));
    }
}

// Function to connect to AssemblyAI's real-time transcription service
async function connectToAssemblyAI(
    socket: WebSocket,
    userId: string,
    apiKeyId: string,
    model: AssemblyAIModel,
    sessionId: string,
    availableMinutes: number
): Promise<void> {
    let isConnected = false;
    let assemblyAISocket: WebSocket | null = null;
    let sessionStartTime = Date.now();
    let audioBytesSent = 0;
    let transcriptionReceived = false;
    let sessionTimeout: number | null = null;

    // Set a timeout for the maximum session duration (minimum of available minutes or 20 minutes)
    const maxDurationMs = Math.min(availableMinutes, MAX_SESSION_DURATION_MINUTES) * 60 * 1000;

    console.log(`Starting AssemblyAI connection setup for session ${sessionId}`, {
        model: model,
        userId: userId,
        apiKeyId: apiKeyId,
        availableMinutes: availableMinutes,
        maxDurationMs: maxDurationMs,
        modelForPricing: `assemblyai/${model}-realtime`
    });

    try {
        // Record pending usage
        await supabase
            .from('usage_history')
            .insert({
                user_id: userId,
                api_key_id: apiKeyId,
                service: 'transcription',
                model: `assemblyai/${model}-realtime`,
                amount: 0, // Will be updated when the session ends
                cost: 0,   // Will be updated when the session ends
                pricing_model: 'credits', // Default to credits, will be updated later
                status: 'pending',
                metadata: {
                    sessionId,
                    startTime: sessionStartTime,
                    maxDurationMs
                }
            });

        console.log(`Recorded pending usage for session ${sessionId}`);

        // Set up session timeout
        sessionTimeout = setTimeout(() => {
            console.log(`Session ${sessionId} reached maximum duration of ${maxDurationMs}ms`);

            // Send timeout notification to client
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'timeout',
                    message: 'Session reached maximum duration'
                }));
            }

            // Close AssemblyAI connection if still open
            if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                assemblyAISocket.close();
            }

            // Close client connection if still open
            if (socket.readyState === WebSocket.OPEN) {
                socket.close();
            }
        }, maxDurationMs);

        // Generate a temporary token for AssemblyAI
        const temporaryToken = await generateAssemblyAITemporaryToken(Math.min(availableMinutes * 60, 1200)); // Convert minutes to seconds, max 20 minutes (1200 seconds)

        console.log(`Got AssemblyAI token for session ${sessionId}:`, {
            tokenLength: temporaryToken.length,
            tokenPrefix: temporaryToken.substring(0, 5) + '...'
        });

        // Connect to AssemblyAI's real-time transcription service
        // Use the token directly in the URL as a query parameter to avoid subprotocol issues
        // IMPORTANT: Only include sample_rate and token in the URL, matching the direct test
        const assemblyAIUrl = `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${temporaryToken}`;

        console.log(`Creating AssemblyAI WebSocket connection for session ${sessionId}`);

        // Create WebSocket connection without using subprotocols
        assemblyAISocket = new WebSocket(assemblyAIUrl);

        assemblyAISocket.onopen = () => {
            console.log(`Connected to AssemblyAI for session ${sessionId}`);
            isConnected = true;

            // // Send configuration to AssemblyAI
            // const config = {
            //     sample_rate: 16000,
            //     encoding: "pcm_s16le",
            //     word_boost: [],
            // };

            // console.log(`Sending configuration to AssemblyAI for session ${sessionId}:`, config);

            // // Add a short delay before sending the configuration
            // setTimeout(() => {
            //     if (assemblyAISocket?.readyState === WebSocket.OPEN) {
            //         assemblyAISocket.send(JSON.stringify(config));
            //     }
            // }, 500);

            // Notify client that connection is ready
            socket.send(JSON.stringify({
                type: 'connected',
                sessionId: sessionId,
                maxDurationMs: maxDurationMs
            }));
        };

        assemblyAISocket.onmessage = (event) => {
            // Forward transcription results to the client
            try {
                const data = JSON.parse(event.data);
                console.log(`Received AssemblyAI message for session ${sessionId}:`, {
                    type: data.message_type || 'unknown',
                    hasText: !!data.text,
                    textLength: data.text?.length || 0
                });

                if (data.message_type === 'FinalTranscript' || data.message_type === 'PartialTranscript') {
                    transcriptionReceived = true;
                    socket.send(event.data);
                } else if (data.message_type === 'SessionBegins') {
                    console.log(`AssemblyAI session started for ${sessionId}:`, data);
                } else if (data.message_type === 'Error') {
                    console.error(`AssemblyAI error for session ${sessionId}:`, data);
                    socket.send(JSON.stringify({
                        type: 'error',
                        message: data.text || 'Error from transcription service'
                    }));
                }
            } catch (error) {
                console.error(`Error parsing AssemblyAI message for session ${sessionId}:`, {
                    error: error,
                    rawData: typeof event.data === 'string' ? event.data.substring(0, 200) : 'Binary data'
                });
            }
        };

        assemblyAISocket.onerror = (event) => {
            console.error(`AssemblyAI WebSocket error for session ${sessionId}:`, event);

            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'error',
                    message: 'Error in transcription service',
                    details: 'WebSocket connection error'
                }));
            }
        };

        assemblyAISocket.onclose = (event) => {
            console.log(`AssemblyAI WebSocket closed for session ${sessionId}`, {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
                transcriptionReceived: transcriptionReceived,
                audioBytesSent: audioBytesSent,
                durationMs: Date.now() - sessionStartTime
            });

            // Handle specific close codes
            if (event.code === 4000) {
                console.error(`AssemblyAI error: Invalid parameters`);
            } else if (event.code === 4001) {
                console.error(`AssemblyAI error: Authentication failed`);
            } else if (event.code === 4101) {
                console.error(`AssemblyAI error: Invalid request format - ${event.reason}`);
            }

            isConnected = false;

            // Clear the session timeout
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Notify client that AssemblyAI connection is closed
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'service_disconnected',
                    code: event.code,
                    reason: event.reason,
                    transcriptionReceived: transcriptionReceived,
                    details: event.reason || 'Connection closed'
                }));

                // Close client connection if not already closed
                socket.close();
            }

            // Record final session usage
            finalizeSession(
                userId,
                apiKeyId,
                `assemblyai/${model}-realtime`,
                sessionId,
                sessionStartTime,
                audioBytesSent,
                transcriptionReceived,
                undefined,
                (Date.now() - sessionStartTime) / 1000
            );
        };

        socket.onmessage = (event) => {
            try {
                // Check if the message is binary audio data
                if (event.data instanceof ArrayBuffer) {
                    if (isConnected && assemblyAISocket?.readyState === WebSocket.OPEN) {
                        audioBytesSent += event.data.byteLength;
                        assemblyAISocket.send(event.data);
                    } else if (!isConnected) {
                        console.log(`Cannot forward audio for session ${sessionId}: AssemblyAI not connected`);
                    }
                } else {
                    // Handle JSON control messages from client
                    const message = JSON.parse(event.data);
                    console.log(`Received client control message for session ${sessionId}:`, message);

                    if (message.type === 'close') {
                        // Client requested to close the connection
                        console.log(`Client requested close for session ${sessionId}`);

                        if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                            assemblyAISocket.close();
                        }

                        if (sessionTimeout !== null) {
                            clearTimeout(sessionTimeout);
                            sessionTimeout = null;
                        }

                        socket.close();
                    }
                }
            } catch (error) {
                console.error(`Error handling client message for session ${sessionId}:`, error);
            }
        };

        socket.onerror = (event) => {
            console.error(`Client WebSocket error for session ${sessionId}:`, event);
        };

        socket.onclose = () => {
            console.log(`Client WebSocket closed for session ${sessionId}`);

            // Clear the session timeout
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Close AssemblyAI connection if still open
            if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                assemblyAISocket.close();
            }

            // Record final session usage
            finalizeSession(
                userId,
                apiKeyId,
                `assemblyai/${model}-realtime`,
                sessionId,
                sessionStartTime,
                audioBytesSent,
                transcriptionReceived,
                undefined,
                (Date.now() - sessionStartTime) / 1000
            );
        };
    } catch (error) {
        console.error(`Error setting up AssemblyAI connection for session ${sessionId}:`, error);

        if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to establish connection with AssemblyAI',
                details: error.message
            }));
            socket.close();
        }

        // Clear the session timeout
        if (sessionTimeout !== null) {
            clearTimeout(sessionTimeout);
            sessionTimeout = null;
        }
    }

    // After finalizing the session, send the duration information to the client
    if (socket.readyState === WebSocket.OPEN) {
        // Calculate duration in seconds
        const durationSeconds = (Date.now() - sessionStartTime) / 1000;
        
        // Send duration information to client
        socket.send(JSON.stringify({
            type: 'duration',
            duration: durationSeconds,
            processingComplete: true,
            sessionId: sessionId
        }));
        
        // Then send the finalization confirmation
        socket.send(JSON.stringify({
            type: 'finalized',
            message: 'Session finalized successfully',
            sessionId: sessionId
        }));
    }
}

// Helper function to finalize a session and record usage
async function finalizeSession(
    userId: string,
    apiKeyId: string,
    model: string,
    sessionId: string,
    startTime: number,
    audioBytesSent: number,
    transcriptionReceived: boolean,
    tokenInfo?: { input_tokens: number, output_tokens: number },
    processedDurationSeconds?: number
): Promise<void> {
    console.log(`Finalizing session ${sessionId} for model ${model}`, {
        audioBytesSent,
        transcriptionReceived,
        durationMs: Date.now() - startTime,
        hasTokenInfo: !!tokenInfo,
        processedDurationSeconds
    });

    try {
        // Use the new finalize_realtime_session database function
        await supabase.rpc('finalize_realtime_session', {
            p_user_id: userId,
            p_api_key_id: apiKeyId,
            p_model: model,
            p_session_id: sessionId,
            p_start_time: startTime,
            p_audio_bytes_sent: audioBytesSent,
            p_has_transcription: transcriptionReceived,
            p_token_info: tokenInfo || null,
            p_processed_duration_seconds: processedDurationSeconds || null
        });
    } catch (error) {
        console.error(`Error finalizing session ${sessionId}:`, error);
    }
}

// Export the handler function for use in index.ts
export async function handleRealTimeRequest(req: Request): Promise<Response> {
    console.log('Received realtime transcription request:', {
        url: req.url,
        method: req.method,
        upgrade: req.headers.get('upgrade'),
        origin: req.headers.get('origin'),
        host: req.headers.get('host'),
    });

    try {
        // Check if the request is a WebSocket upgrade request
        const upgrade = req.headers.get('upgrade') || '';
        if (upgrade.toLowerCase() !== 'websocket') {
            console.error('Request is not trying to upgrade to WebSocket');
            return new Response("Request isn't trying to upgrade to WebSocket.", { status: 400 });
        }

        // Get API key and service parameters from URL
        const url = new URL(req.url);
        const apiKey = url.searchParams.get('apiKey');
        const service = (url.searchParams.get('service') || 'openai') as TranscriptionService;
        const model = url.searchParams.get('model') || '';
        const language = url.searchParams.get('language') || 'en';

        console.log('Request parameters:', {
            service,
            model,
            language,
            hasApiKey: !!apiKey,
            apiKeyLength: apiKey ? apiKey.length : 0
        });

        if (!apiKey) {
            console.error('Missing API key in query parameters');
            return new Response('Missing API key', { status: 401 });
        }

        // Validate service and model
        if (!SUPPORTED_SERVICES.includes(service)) {
            return createErrorResponse(400, `Unsupported service. Must be one of: ${SUPPORTED_SERVICES.join(', ')}`, ErrorCode.INVALID_REQUEST);
        }

        // Get the service models based on the requested service
        const serviceModels = SUPPORTED_MODELS[service];

        // For OpenAI service, only GPT-4o models support real-time
        if (service === 'openai' && !['gpt-4o-mini-transcribe', 'gpt-4o-transcribe'].includes(model)) {
            return createErrorResponse(400, 'Only GPT-4o models support real-time transcription', ErrorCode.UNSUPPORTED_MODEL);
        }

        // If no model is specified, use the default for the service
        const defaultModel = service === 'openai'
            ? 'gpt-4o-mini-transcribe' as OpenAIModel
            : 'best' as AssemblyAIModel;

        const selectedModel = model || defaultModel;

        // Validate the model
        if (!(serviceModels as readonly string[]).includes(selectedModel)) {
            return createErrorResponse(400, `Unsupported model for ${service}. Must be one of: ${serviceModels.join(', ')}`, ErrorCode.UNSUPPORTED_MODEL);
        }

        console.log('Validating API key:', {
            keyLength: apiKey.length,
            keyPrefix: apiKey.substring(0, 5) + '...'
        });

        // Validate VoiceHype API key and get user info
        const { data: validationData, error: validationError } = await supabase
            .rpc('validate_api_key', { p_key: apiKey });

        if (validationError || !validationData || validationData.length === 0) {
            console.error('API key validation failed:', validationError);
            return createErrorResponse(401, 'Invalid API key', ErrorCode.UNAUTHORIZED);
        }

        const userId = validationData[0].user_id;
        const apiKeyId = validationData[0].api_key_id;

        console.log('API key validated successfully for user:', userId);
        console.log(`Real-time transcription request: service=${service}, model=${selectedModel}, language=${language}`);

        // Check for unpaid balances
        const { data: hasUnpaidBalance, error: balanceCheckError } = await supabase
            .rpc('has_unpaid_payg_balance', { p_user_id: userId });

        if (balanceCheckError) {
            console.error('Error checking for unpaid balances:', balanceCheckError);
            // Continue anyway with the operation
        } else if (hasUnpaidBalance) {
            // Get detailed unpaid balance information
            const { data: unpaidBalances } = await supabase
                .rpc('get_unpaid_payg_balances', { p_user_id: userId });

            console.log('Unpaid balances:', {
                hasUnpaidBalance,
                balances: unpaidBalances
            });

            return createErrorResponse(
                402,
                'You have unpaid PAYG balances from previous months. Please settle your outstanding balance before using the service.',
                ErrorCode.UNPAID_BALANCE
            );
        }

        // Get available minutes from user credits
        const { data: pricingCheck, error: usagePricingError } = await supabase
            .rpc('check_usage_allowance', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`,
                p_amount: 1, // Check for 1 minute to get the cost per minute
                p_api_key_id: apiKeyId,
                p_is_input_only: false // Explicitly set this to resolve function overloading
            });

        console.log('Usage allowance check result:', {
            modelChecked: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`,
            result: pricingCheck?.[0] || null,
            error: usagePricingError || null
        });

        if (usagePricingError) {
            console.error('Error checking usage allowance:', usagePricingError);
            return createErrorResponse(500, 'Error checkinWg usage allowance', ErrorCode.SERVICE_ERROR);
        }

        if (!pricingCheck || pricingCheck.length === 0 || !pricingCheck[0].can_use) {
            console.error('Insufficient usage allowance:', {
                modelRequested: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`,
                pricingCheck: pricingCheck?.[0] || null,
                service: 'transcription'
            });

            const errorCode = pricingCheck && pricingCheck[0]?.error_code
                ? pricingCheck[0].error_code
                : ErrorCode.INSUFFICIENT_CREDITS;

            return createErrorResponse(
                402,
                'Insufficient credits or quota for this operation',
                errorCode as ErrorCode
            );
        }

        // Get service pricing for this model
        const { data: servicePricing, error: servicePricingError } = await supabase
            .from('service_pricing')
            .select('*')
            .eq('service', 'transcription')
            .eq('model', service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`)
            .eq('is_active', true)
            .single();

        console.log('Service pricing check:', {
            modelRequested: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`,
            found: !!servicePricing,
            pricing: servicePricing ? {
                cost_per_unit: servicePricing.cost_per_unit,
                unit: servicePricing.unit
            } : null,
            error: servicePricingError ? servicePricingError.message : null
        });

        if (servicePricingError || !servicePricing) {
            console.error('Error fetching service pricing:', {
                error: servicePricingError,
                service: 'transcription',
                model: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`
            });
            return createErrorResponse(500, 'Error fetching service pricing', ErrorCode.SERVICE_ERROR);
        }

        // Get user's credits
        const { data: credits, error: creditsError } = await supabase
            .from('credits')
            .select('balance')
            .eq('user_id', userId)
            .single();

        if (creditsError) {
            console.error('Error fetching user credits:', creditsError);
            return createErrorResponse(500, 'Error fetching user credits', ErrorCode.SERVICE_ERROR);
        }

        // Calculate available minutes based on credits and cost per minute
        const costPerMinute = servicePricing.cost_per_unit;
        const availableMinutes = credits ? Math.floor(credits.balance / costPerMinute) : 0;

        console.log('Available transcription minutes:', {
            userId,
            creditsBalance: credits?.balance || 0,
            costPerMinute,
            availableMinutes,
            maxSessionMinutes: Math.min(availableMinutes, MAX_SESSION_DURATION_MINUTES),
            modelRequested: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`
        });

        // Check if user has enough credits for at least one minute
        if (availableMinutes < 1) {
            console.error('Insufficient credits for real-time transcription:', {
                creditsBalance: credits?.balance || 0,
                costPerMinute,
                availableMinutes,
                modelRequested: service === 'openai' ? selectedModel : `assemblyai/${selectedModel}-realtime`
            });
            return createErrorResponse(402, 'Insufficient credits for real-time transcription (minimum 1 minute required)',
                ErrorCode.INSUFFICIENT_CREDITS);
        }

        console.log('Ready to upgrade WebSocket connection, for service:', service, 'model:', selectedModel);

        // Upgrade the connection to WebSocket
        try {
            const { socket, response } = Deno.upgradeWebSocket(req);

            // Create a session ID for tracking
            const sessionId = crypto.randomUUID();
            console.log(`Successfully upgraded WebSocket connection for session ${sessionId}`);

            // Connect to the appropriate service based on the request
            if (service === 'openai') {
                await connectToOpenAI(
                    socket,
                    userId,
                    apiKeyId,
                    selectedModel,
                    language,
                    sessionId,
                    availableMinutes
                );
            } else {
                await connectToAssemblyAI(
                    socket,
                    userId,
                    apiKeyId,
                    selectedModel as AssemblyAIModel,
                    sessionId,
                    availableMinutes
                );
            }

            // Return the WebSocket response
            return response;
        } catch (upgradeError: any) {
            console.error('Error upgrading WebSocket connection:', upgradeError.message, upgradeError.stack);
            return new Response(`Failed to upgrade WebSocket connection: ${upgradeError.message}`, {
                status: 500
            });
        }
    } catch (error: any) {
        console.error('Error in real-time transcription:', error);
        return createErrorResponse(
            500,
            `Real-time transcription error: ${error.message}`,
            ErrorCode.SERVICE_ERROR
        );
    }
}

// Keep the serve function for local testing
// Deno-specific way to check if this is the main module
// @ts-ignore: Deno-specific feature
if (typeof Deno !== "undefined" && Deno.mainModule === import.meta.url) {
    console.log("Starting real-time transcription server...");
    serve(handleRealTimeRequest);
} 