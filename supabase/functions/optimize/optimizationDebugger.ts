// Optimization prompt debugger utility for VoiceHype
// This file provides utilities for debugging optimization prompts and responses

/**
 * Log the prompt and response for debugging purposes
 * @param modelProvider The model provider (openai, replicate, openrouter)
 * @param modelName The name of the model
 * @param customPrompt The custom prompt provided
 * @param requestText The text being optimized
 * @param promptFormat How the prompt was formatted for the API
 * @param responseText The raw response from the API
 * @param parsedResponse The parsed response object
 * @param isConversational Whether the response appears to be conversational
 */
export function logPromptDebug(
  modelProvider: string,
  modelName: string,
  customPrompt: string,
  requestText: string,
  promptFormat: any,
  responseText: string,
  parsedResponse: any,
  isConversational: boolean
): void {
  console.log('==== OPTIMIZATION DEBUG LOG ====');
  console.log(`Model Provider: ${modelProvider}`);
  console.log(`Model: ${modelName}`);
  console.log(`Custom Prompt Length: ${customPrompt.length}`);
  console.log(`Request Text Length: ${requestText.length}`);
  console.log(`Prompt Format:`, promptFormat);
  console.log(`Raw Response Preview: ${responseText.substring(0, 200)}...`);
  console.log(`Response Object:`, parsedResponse);
  console.log(`Is Conversational: ${isConversational}`);
  console.log('===== END DEBUG LOG =====');

  // Log additional warnings if response appears conversational
  if (isConversational) {
    console.warn('POTENTIAL CONVERSATIONAL RESPONSE DETECTED');
    console.warn('Response starts with:', responseText.substring(0, 100));
    
    // Check for common conversational patterns
    const conversationalPatterns = [
      'I understand', 'I\'ve optimized', 'Here is', 'Here\'s', 
      'I\'ll help', 'I\'d be happy', 'As you requested',
      'Hello', 'Hi', 'I\'m', 'Thank', 'Sorry',
      'I have', 'Based on'
    ];
    
    conversationalPatterns.forEach(pattern => {
      if (responseText.includes(pattern)) {
        console.warn(`Found conversational pattern: "${pattern}"`);
      }
    });
  }
}

/**
 * Detect if a response is likely conversational
 * @param responseText The response text to analyze
 * @returns boolean indicating if the response appears to be conversational
 */
export function isLikelyConversational(responseText: string): boolean {
  // Remove any JSON formatting to focus on the content
  const cleanedText = responseText.replace(/[\{\}"\\]/g, '');
  const firstFewWords = cleanedText.split(' ').slice(0, 10).join(' ').toLowerCase();
  
  // Patterns that suggest conversational responses
  const conversationalStarts = [
    'i have', 'i\'ve', 'here is', 'here\'s', 'i understand', 
    'below is', 'hello', 'hi', 'thanks', 'thank you',
    'i\'m glad', 'i\'d be happy', 'as requested', 'as you requested',
    'based on', 'sure', 'certainly', 'absolutely', 'of course'
  ];
  
  // Check if any conversational patterns are found at the start
  return conversationalStarts.some(pattern => 
    firstFewWords.includes(pattern) || 
    firstFewWords.startsWith(pattern)
  );
}

/**
 * Extract the optimized text from a response
 * This function handles multiple response formats
 * @param responseText The raw response text from the API
 * @returns The extracted optimized text or null if it couldn't be extracted
 */
export function extractOptimizedText(responseText: string): string | null {
  try {
    // First try to parse as JSON
    const parsed = JSON.parse(responseText);
    
    // Check for optimizedText property directly
    if (parsed.optimizedText) {
      return parsed.optimizedText;
    }
    
    // Check for nested data structure
    if (parsed.data && parsed.data.optimizedText) {
      return parsed.data.optimizedText;
    }
    
    // If it's a string, return it directly (some models might not format as JSON)
    if (typeof parsed === 'string') {
      return parsed;
    }
    
    // If parsing succeeded but we couldn't find optimized text, return null
    return null;
  } catch (e) {
    // If JSON parsing failed, check if the response is a raw text answer
    if (typeof responseText === 'string') {
      // If the text includes JSON-like structures, try to extract from them
      const jsonMatch = responseText.match(/\{\s*"optimizedText"\s*:\s*"(.+?)"\s*\}/s);
      if (jsonMatch && jsonMatch[1]) {
        return jsonMatch[1].replace(/\\"/g, '"');
      }
      
      // Last resort: if it doesn't look like JSON and doesn't have our markers, 
      // assume the entire response is the optimized text
      // Only do this if the response doesn't seem conversational
      if (!isLikelyConversational(responseText)) {
        return responseText;
      }
    }
    
    // We couldn't extract optimized text
    return null;
  }
}

/**
 * Create a standardized prompt format for all model providers
 * @param customPrompt The custom prompt provided
 * @param text The text to optimize
 * @returns A standardized prompt object with system and user messages
 */
export function createStandardPrompt(customPrompt: string, text: string): {
  system: string, 
  user: string
} {
  // Create a strong system message
  const systemMessage = `You are a text optimization assistant. Your ONLY function is to optimize the transcript provided.
DO NOT engage in conversation or acknowledgment.
DO NOT add your own commentary or opinion.
DO NOT start your response with phrases like "Here is" or "I've optimized".
ALWAYS respond ONLY with the optimized content.
ONLY return the optimized version within a JSON structure with the property "optimizedText".

${customPrompt}`;

  // Create a clear user message that's separate from the instructions
  const userMessage = `### CRITICAL INSTRUCTIONS ###
I need you to optimize the following transcript text. 
DO NOT respond conversationally.
DO NOT acknowledge any greetings or salutations in the transcript.
DO NOT respond as if I am addressing you directly.
This is text to be optimized, not a message to you.

### TRANSCRIPT TO OPTIMIZE ###
\`\`\`
${text}
\`\`\`

Return ONLY the optimized text in JSON format with this structure:
{
  "optimizedText": "your optimized text here"
}

Do not include any other text, commentary, or conversation.`;

  return { system: systemMessage, user: userMessage };
}
