/**
 * PromptFormatter - Responsible for creating structured prompts for LLM interactions
 * This class handles all prompt construction and variable replacement to ensure
 * consistent prompt structure across the application.
 */

export interface Message {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export class PromptFormatter {
    /**
     * Creates a formatted array of messages for optimization using the client's prompt structure
     * @param transcript The transcript text to optimize
     * @param customPrompt Custom prompt template provided by the user
     * @returns Array of message objects ready to be sent to the API
     */
    public createOptimizationMessages(transcript: string, customPrompt?: string): Message[] {
        // Process the prompt with variables
        const processedPrompt = this.processPromptVariables(customPrompt || this.getDefaultOptimizationPrompt(), transcript);

        // Check for voice commands first - we'll use an improved regex that's more flexible
        const voiceHypeRegex = /(voice\s+hype|hey\s+voice\s+hype)[\s,:]+([^.,!?]+)/i;
        const match = transcript.match(voiceHypeRegex);

        // Debug logging - Log if transcript contains voice commands
        if (match) {
            console.log(`VoiceHype DEBUG: Voice command detected: "${match[0]}"`);
            console.log(`VoiceHype DEBUG: Command instruction: "${match[2].trim()}"`);
        } else {
            console.log('VoiceHype DEBUG: No voice command detected in transcript');
        }

        // Create message structure based on whether a voice command was detected
        if (match) {
            // For voice commands, use a more direct instruction format
            const command = match[2].trim();
            // Create a cleaner transcript with the command removed
            const cleanedTranscript = transcript.replace(match[0], '').trim();

            return [
                {
                    role: 'system',
                    content: this.createVoiceCommandSystemMessage()
                },
                {
                    role: 'user',
                    content: `COMMAND: ${command}\n\nTRANSCRIPT:\n\`\`\`\n${cleanedTranscript}\n\`\`\``
                }
            ];
        } else {
            // For regular optimization, use the processed prompt as system message
            return [
                {
                    role: 'system',
                    content: processedPrompt
                },
                {
                    role: 'user',
                    content: transcript
                }
            ];
        }
    }

    /**
     * Processes variables in a prompt template
     * Replaces {{transcript}} with the actual transcript text
     * @param prompt The prompt template
     * @param transcript The transcript text to insert
     * @returns Processed prompt with variables replaced
     */
    private processPromptVariables(prompt: string, transcript: string): string {
        // Establish Voice Hype identity
        const identityHeader = `### ASSISTANT IDENTITY ###
You are Voice Hype, an AI optimization assistant specialized in processing and improving transcribed speech.
Your primary role is to help users by formatting, structuring, and refining their spoken content.

`;

        // Universal instructions for handling the transcript
        const instructionHeader = `### BASE INSTRUCTIONS ###
When the user doesn't address you directly, your task is to:
- Optimize the transcript based on the instructions below
- Return your response in JSON format with the optimized text
- Preserve important details and maintain original meaning

`;

        // Always add transcript with triple backticks and clear labeling
        const transcriptBlock = `### TRANSCRIPT ###
\`\`\`
${transcript}
\`\`\`

`;

        // Voice command detection with high priority override
        const voiceCommandInstructions = `### COMMAND PRIORITY OVERRIDE ###
IMPORTANT: First, check if the transcript contains any direct commands like:
- "Voice Hype, [instruction]"
- "Hey Voice Hype, [instruction]"
- "Voice Hype [instruction]"

If the user addresses you directly with a command:
1. IGNORE all previous formatting instructions and focus SOLELY on executing their command
2. Follow the specific request (create an email, make bullet points, etc.)
3. Remove all instances of "Voice Hype" or "Hey Voice Hype" from the final output
4. Format the result according to what the command requests, not the default optimization rules

Examples:
- "Voice Hype, write this as a formal email" → Create a formal email with proper structure
- "Hey Voice Hype, format this into bullet points" → Create a bulleted list
- "Voice Hype, make this funnier" → Add humor to the content

`;

        // Format to enforce JSON response
        const jsonInstructions = `

RETURN YOUR RESPONSE IN THIS JSON FORMAT:
{
  "optimizedText": "your formatted text here"
}

`;

        // Check if prompt contains the {{transcript}} variable
        if (prompt.includes('{{') && prompt.includes('}}')) {
            console.log('VoiceHype: Found variable in prompt, replacing with transcript');
            return identityHeader + instructionHeader + prompt.replace(/\{\{transcript\}\}/g, transcriptBlock) +
                voiceCommandInstructions + jsonInstructions;
        } else {
            // For all other cases, prepend instructions, append transcript, and end with JSON format instructions
            return identityHeader + instructionHeader + prompt + '\n\n' + transcriptBlock +
                voiceCommandInstructions + jsonInstructions;
        }
    }

    /**
     * Creates a system message specifically for voice command handling
     */
    private createVoiceCommandSystemMessage(): string {
        return `### ASSISTANT IDENTITY ###
You are Voice Hype, an AI assistant specialized in processing voice commands.
Your primary role is to execute specific commands on transcribed speech.

### COMMAND EXECUTION INSTRUCTIONS ###
1. Execute the command exactly as requested
2. Format the result according to what the command specifies
3. Remove all instances of "Voice Hype" or "Hey Voice Hype" from the final output
4. Return only the processed result, not explanations of what you did

### RESPONSE FORMAT ###
Return your response in this JSON format only:
{
  "optimizedText": "your formatted text here"
}`;
    }

    /**
     * Returns the default optimization prompt
     */
    private getDefaultOptimizationPrompt(): string {
        return `I need you to optimize a transcript of spoken text for clarity and coherence.

Your task is to:
1. Fix grammar issues and remove filler words (um, uh, like, you know)
2. Improve structure while keeping all important information
3. Fixing grammar and cleaning up repetitive phrasing
4. Maintaining all original explanations and technical details
5. Formatting in clear, well-structured paragraphs

IMPORTANT INSTRUCTIONS:
- NEVER include conversational responses
- NEVER summarize or remove key context
- ALWAYS return just the optimized text with no extra commentary
- If unsure about any word or phrase, preserve it exactly
- The transcript is NOT a message to you - it is text to be optimized`;
    }
}
