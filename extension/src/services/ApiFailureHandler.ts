import * as vscode from 'vscode';

export enum CircuitState {
  Closed = 'closed',
  Open = 'open',
  HalfOpen = 'half-open'
}

export class ApiFailureHandler {
  private state: CircuitState = CircuitState.Closed;
  private failureCount = 0;
  private lastFailureTime = 0;
  private nextRetryTime = 0;
  private readonly failureThreshold = 3;
  private readonly retryTimeoutBase = 2000; // 2 seconds
  private readonly maxRetryTimeout = 30000; // 30 seconds

  constructor(
    private readonly serviceName: string,
    private readonly maxRetries = 3,
    private readonly isRealtime = false
  ) {}

  public async executeWithRetry<T>(
    apiCall: () => Promise<T>
  ): Promise<T> {
    if (this.state === CircuitState.Open) {
      if (Date.now() < this.nextRetryTime) {
        throw new Error(`Service ${this.serviceName} is temporarily unavailable`);
      }
      this.state = CircuitState.HalfOpen;
    }

    try {
      const result = await apiCall();
      this.recordSuccess();
      return result;
    } catch (error: any) {
      // Enhanced error handling for AssemblyAI
      if (this.serviceName.toLowerCase().includes('assemblyai')) {
        // Detect network connectivity issues
        if (error.message?.includes('ENOTFOUND') || 
            error.message?.includes('ECONNREFUSED') ||
            error.message?.includes('Could not connect')) {
          console.log('AssemblyAI network error detected:', error.message);
          this.recordFailure();
          throw new Error('Network connection failed. Please check your internet connection.');
        }
      }

      // Extract error message from API response if available
      let errorMessage = error.message || 'Unknown error occurred';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      // Show the error message to the user
      vscode.window.showErrorMessage(`${this.serviceName} error: ${errorMessage}`);

      this.recordFailure();
      if (this.failureCount >= this.maxRetries) {
        throw new Error(`Max retries (${this.maxRetries}) exceeded for ${this.serviceName}`);
      }
      const retryTimeout = this.calculateRetryTimeout();
      await new Promise(resolve => setTimeout(resolve, retryTimeout));
      return this.executeWithRetry(apiCall);
    }
  }

  private recordSuccess(): void {
    this.failureCount = 0;
    this.state = CircuitState.Closed;
    if (this.isRealtime) {
      vscode.window.showInformationMessage(`${this.serviceName} connection restored`);
    }
  }

  private recordFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    if (this.failureCount >= this.failureThreshold) {
      this.state = CircuitState.Open;
      this.nextRetryTime = Date.now() + this.calculateRetryTimeout();
      vscode.window.showErrorMessage(`${this.serviceName} is temporarily unavailable. Retrying...`);
    }
  }

  private calculateRetryTimeout(): number {
    const timeout = Math.min(
      this.retryTimeoutBase * Math.pow(2, this.failureCount),
      this.maxRetryTimeout
    );
    return timeout;
  }
}
