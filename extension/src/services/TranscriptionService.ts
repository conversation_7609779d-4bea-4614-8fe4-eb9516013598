import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as vscode from 'vscode';
import { ITranscriptionService } from '../models/interfaces';
import { IConfigurationService } from '../models/interfaces';
import { transcribeAudio, transcribeAndOptimize, optimizeWithMessages } from '../utils/supabaseClient';
import { WebSocket } from 'ws';
import { RealtimeConnectionManager } from './RealtimeConnectionManager';
import { ApiFailureHandler } from './ApiFailureHandler';
import { PromptFormatter } from '../utils/PromptFormatter';

// Define new status event types
export enum TranscriptionStatus {
    Initializing = 'initializing',
    ConnectingWebSocket = 'connecting_websocket',
    WebSocketConnected = 'websocket_connected',
    SessionEstablished = 'session_established',
    StreamingAudio = 'streaming_audio',
    TranscriptionReceived = 'transcription_received',
    TranscriptionComplete = 'transcription_complete',
    UsageReceived = 'usage_received',
    AllChunksProcessed = 'all_chunks_processed',
    Reconnecting = 'reconnecting',
    Reconnected = 'reconnected',
    Error = 'error'
}

export class TranscriptionService implements ITranscriptionService {
    private recordingPath: string;
    private statusListeners: ((status: TranscriptionStatus, message?: string) => void)[] = [];

    private apiFailureHandler: ApiFailureHandler;

    constructor(private configService: IConfigurationService) {
        this.apiFailureHandler = new ApiFailureHandler('TranscriptionService');
        // Use system's temporary directory
        const tmpDir = os.tmpdir();
        // Use different filename based on platform
        if (os.platform() === 'linux') {
            this.recordingPath = path.join(tmpDir, 'voicehype_recording.wav');
        } else {
            this.recordingPath = path.join(tmpDir, 'voicehype_temp_recording.wav');
        }
        console.log('VoiceHype: Using temporary recording file:', this.recordingPath);

        // Make sure the temp directory is accessible
        try {
            fs.accessSync(tmpDir, fs.constants.W_OK);
            console.log(`VoiceHype: Temporary directory is writable: ${tmpDir}`);
        } catch (error) {
            console.error(`VoiceHype: Temporary directory is not writable: ${tmpDir}`, error);
            vscode.window.showErrorMessage(`VoiceHype: Cannot write to temporary directory. Please check permissions.`);
        }

        // Check if we need to create a placeholder file
        if (!fs.existsSync(this.recordingPath)) {
            try {
                // Create an empty file to ensure the path is valid
                fs.writeFileSync(this.recordingPath, Buffer.alloc(0));
                console.log(`VoiceHype: Created placeholder file at: ${this.recordingPath}`);
                // Delete it immediately - we just wanted to validate we can write there
                fs.unlinkSync(this.recordingPath);
            } catch (error) {
                console.error(`VoiceHype: Error creating/testing placeholder file: ${error}`);
            }
        }
    }

    // Add method to register status listeners
    addStatusListener(listener: (status: TranscriptionStatus, message?: string) => void): void {
        this.statusListeners.push(listener);
    }

    // Add method to emit status events
    private emitStatus(status: TranscriptionStatus, message?: string): void {
        console.log(`VoiceHype: Status update - ${status}${message ? `: ${message}` : ''}`);
        this.statusListeners.forEach(listener => listener(status, message));
    }

    async transcribeAudio(
        filePath: string,
        optimize: boolean = false,
        translate: boolean = false,
        realtime: boolean = false,
        customPrompt?: string
    ): Promise<{
        transcription: string;
        optimizedText?: string;
        duration: number;
    }> {
        console.log('VoiceHype: Transcribing audio with optimize flag:', optimize, 'translate flag:', translate, 'realtime flag:', realtime);
        console.log('VoiceHype: Custom prompt provided:', customPrompt);
        console.log('VoiceHype: Input file path:', filePath);
        console.log('VoiceHype: Target recording path:', this.recordingPath);

        // Get configuration settings
        const language = this.configService.getTranscriptionLanguage();
        const model = this.configService.getTranscriptionModel();
        const service = this.configService.getTranscriptionService();

        console.log(`VoiceHype: Using model: ${model}, service: ${service}, language: ${language}, translate: ${translate}, realtime: ${realtime}`);

        try {
            this.emitStatus(TranscriptionStatus.Initializing);

            // Determine which file to use - if filePath is the same as our recordingPath, use it directly
            const fileToUse = filePath === this.recordingPath ? filePath : this.recordingPath;

            // On Windows, the microphone implementation writes directly to our recordingPath
            // so we can skip the copy step if we detect we're already using the right file
            if (filePath !== this.recordingPath) {
            // Copy the recording with retry logic for network failures
            const maxRetries = 3;
            let retryCount = 0;
            let lastError: Error | null = null;
            
            while (retryCount < maxRetries) {
                try {
                    await fs.promises.copyFile(filePath, this.recordingPath);
                    console.log(`VoiceHype: Copied recording from ${filePath} to temporary location: ${this.recordingPath}`);
                    break;
                } catch (error: any) {
                    retryCount++;
                    lastError = error;
                    
                    // Check for network-related errors
                    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || 
                        error.message.includes('network') || error.message.includes('connect')) {
                        const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 30000);
                        console.log(`VoiceHype: Network error during file copy (attempt ${retryCount}/${maxRetries}), retrying in ${retryDelay}ms`);
                        vscode.window.showWarningMessage(`Network issue detected. Retrying upload (${retryCount}/${maxRetries})...`);
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    } else {
                        throw error;
                    }
                }
            }
            
            if (retryCount === maxRetries && lastError) {
                vscode.window.showErrorMessage('Failed to upload audio file after multiple attempts. Please check your internet connection.');
                throw new Error(`File upload failed after ${maxRetries} attempts: ${lastError.message}`);
            }
            } else {
                console.log(`VoiceHype: Using recording file directly: ${this.recordingPath}`);
            }

            // Check if the file exists and has data
            if (!fs.existsSync(this.recordingPath)) {
                throw new Error(`Recording file not found at ${this.recordingPath}`);
            }

            const stats = fs.statSync(this.recordingPath);
            console.log(`VoiceHype: Recording file size: ${stats.size} bytes`);

            if (stats.size < 44) { // WAV header is at least 44 bytes
                throw new Error('Recording file is empty or invalid. Please try recording again.');
            }

            // Calculate accurate audio duration before sending to API
            const accurateDuration = await this.getAudioDuration(this.recordingPath);
            console.log(`VoiceHype: Accurate audio duration (pre-API): ${accurateDuration.toFixed(3)} seconds`);

            // Read the audio file as base64
            const audioData = await fs.promises.readFile(this.recordingPath);

            // Check if we have valid audio data
            if (!audioData || audioData.length === 0) {
                console.error(`VoiceHype: No audio data found in file: ${this.recordingPath}`);
                console.log(`VoiceHype: File exists: ${fs.existsSync(this.recordingPath)}`);

                if (fs.existsSync(this.recordingPath)) {
                    // Get file stats to see if it has content
                    const stats = fs.statSync(this.recordingPath);
                    console.log(`VoiceHype: File size: ${stats.size} bytes`);

                    if (stats.size === 0) {
                        throw new Error('Recording file is empty. Please try recording again.');
                    }
                } else {
                    throw new Error('Recording file not found. Please try recording again.');
                }
            }

            const base64Audio = audioData.toString('base64');

            console.log(`VoiceHype: Audio data prepared, length: ${base64Audio.length}`);

            // Verify we have valid base64 data
            if (!base64Audio || base64Audio.length === 0) {
                throw new Error('Failed to convert audio to base64 format.');
            }

            let result;

            if (realtime) {
                // Use real-time transcription mode
                console.log('VoiceHype: Using real-time transcription mode');
                console.log('VoiceHype: Audio buffer size:', audioData.buffer.byteLength);
                const transcription = await this.transcribeAudioRealtime(audioData.buffer, language);
                console.log('VoiceHype: Real-time transcription complete, result:', transcription);

                this.emitStatus(TranscriptionStatus.TranscriptionComplete);

                // Use our accurately calculated duration
                result = {
                    transcription,
                    duration: accurateDuration
                };
            } else if (optimize) {
                // Get optimization settings
                const optimizationModel = this.configService.getOptimizationModel();

                // Get the custom prompt or let PromptFormatter use the default
                const optimizationPrompt = customPrompt && customPrompt.trim() !== '' ? customPrompt : '';

                console.log(`VoiceHype: Using optimization model: ${optimizationModel}`);
                console.log('VoiceHype: Using optimization prompt:', optimizationPrompt);
                console.log('VoiceHype: Calling transcribeAndOptimize with optimize flag:', optimize, 'translate flag:', translate);

                // Include our accurate duration in the metadata
                const metadata = {
                    accurateDuration: accurateDuration,
                };

            // Call transcribeAndOptimize directly since it handles both transcription and optimization
            let transcriptionResponse;
            let optimizedText = '';
            try {
                const optimizationResponse = await this.apiFailureHandler.executeWithRetry(() =>
                    transcribeAndOptimize(
                    base64Audio,
                    model,
                    optimizationModel,
                    optimizationPrompt,
                    service,
                    language,
                    translate,
                    realtime,
                    metadata
                    )
                );
                // Extract both transcription and optimized text from the single response
                transcriptionResponse = {
                    transcription: optimizationResponse.transcription,
                    duration: optimizationResponse.duration
                };
                optimizedText = optimizationResponse.optimizedText;
            } catch (error: unknown) {
                console.error('Optimization failed, falling back to original transcription', error);
                const errorMsg = error instanceof Error ? error.message.toLowerCase() : 'unknown error';
                if (errorMsg.includes('network') || errorMsg.includes('connection') || errorMsg.includes('econn')) {
                    vscode.window.showWarningMessage('Network disconnected during optimization - using original transcription');
                } else {
                    vscode.window.showWarningMessage('Optimization failed - using original transcription');
                }
            }

                console.log('VoiceHype: Transcription and optimization results:', {
                    hasTranscription: transcriptionResponse ? !!transcriptionResponse.transcription : false,
                    hasOptimizedText: !!optimizedText,
                    duration: accurateDuration
                });

                // Use our accurate duration and the transcription/optimization results
                result = {
                    transcription: transcriptionResponse ? transcriptionResponse.transcription : '',
                    optimizedText: optimizedText,
                    duration: accurateDuration
                };
            } else {
                // Just transcribe without optimization
                console.log('VoiceHype: Calling transcribeAudio without optimization');

                // Include our accurate duration in the metadata
                const metadata = {
                    accurateDuration: accurateDuration
                };

                const response = await this.apiFailureHandler.executeWithRetry(() =>
                    transcribeAudio(
                    base64Audio,
                    model,
                    service,
                    language,
                    translate,
                    realtime,
                    metadata
                    )
                );

                // Use our accurate duration instead of the API's duration
                result = {
                    transcription: response.transcription,
                    duration: accurateDuration > 0 ? accurateDuration : (response.duration || 0)
                };
            }

            console.log('VoiceHype: Transcription result:', {
                hasTranscription: !!result.transcription,
                hasOptimizedText: !!result.optimizedText,
                duration: result.duration
            });

            return result;
        } catch (error: any) {
            console.error(`VoiceHype: Transcription error: ${error.message}`);
            throw error;
        }
    }

    async optimizeText(text: string, customPrompt?: string): Promise<string> {
        if (customPrompt && customPrompt.trim() !== '') {
            console.log('VoiceHype: Optimizing text with provided custom prompt');
            return this.optimizeWithCustomPrompt(text, customPrompt);
        }

        console.log('VoiceHype: Optimizing text with default prompt');
        // Pass empty string to use the default prompt from PromptFormatter
        return this.optimizeWithCustomPrompt(text, '');
    }

    async optimizeWithCustomPrompt(text: string, customPrompt: string): Promise<string> {
        console.log('VoiceHype: Optimizing text with custom prompt');
        console.log(`VoiceHype: Custom prompt length: ${customPrompt.length}`);
        console.log(`VoiceHype: Custom prompt preview: ${customPrompt.substring(0, 100)}${customPrompt.length > 100 ? '...' : ''}`);

        try {
            // Use the PromptFormatter to create structured messages
            const promptFormatter = new PromptFormatter();
            const clientMessages = promptFormatter.createOptimizationMessages(text, customPrompt);
            
            console.log(`VoiceHype: Created ${clientMessages.length} message(s) for optimization`);
            console.log(`VoiceHype: System message length: ${clientMessages[0]?.content?.length || 0}`);
            console.log(`VoiceHype: System message preview: ${clientMessages[0]?.content?.substring(0, 100) || ''}${clientMessages[0]?.content?.length > 100 ? '...' : ''}`);

            // Get the selected optimization model
            const optimizationModel = this.configService.getOptimizationModel();
            console.log(`VoiceHype: Using optimization model: ${optimizationModel}`);

            const optimizedText = await optimizeWithMessages(text, optimizationModel, clientMessages);
            
            return optimizedText;
        } catch (error: any) {
            console.error('VoiceHype: Custom optimization error:', error.message);
            throw error;
        }
    }

    private async transcribeAudioRealtime(audioData: ArrayBuffer, language: string): Promise<string> {
        const service = this.configService.getTranscriptionService();
        const model = this.configService.getTranscriptionModel();
        const apiKey = this.configService.getApiKey();

        console.log('VoiceHype: Starting real-time transcription with:', {
            service,
            model,
            language,
            audioDataSize: audioData.byteLength
        });

        if (!apiKey) {
            this.emitStatus(TranscriptionStatus.Error, 'API key not set');
            throw new Error('API key not set');
        }

        return new Promise((resolve, reject) => {
            let transcript = '';
            let lastPartialTranscript = ''; // Store the last partial transcript
            let ws: WebSocket | undefined;
            let isConnected = false;
            // Set a timeout to prevent hanging
            const connectionTimeout = setTimeout(() => {
                console.error('VoiceHype: Connection timeout after 30 seconds');
                this.emitStatus(TranscriptionStatus.Error, 'Connection timeout after 30 seconds');
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                resolve(transcript || lastPartialTranscript); // Return whatever we have
            }, 30000);

            const connectWebSocket = () => {
                this.emitStatus(TranscriptionStatus.ConnectingWebSocket);

                // Connect to our self-hosted Supabase real-time endpoint
                const supabaseUrl = 'supabase.voicehype.ai/functions/v1/transcribe';

                // Properly encode the API key for the URL to avoid platform-specific issues
                const encodedApiKey = encodeURIComponent(apiKey);
                const encodedLanguage = encodeURIComponent(language);

                const wsUrl = `wss://${supabaseUrl}/realtime?apiKey=${encodedApiKey}&service=${service}&model=${model}&language=${encodedLanguage}`;

                console.log(`VoiceHype: Connecting to real-time transcription WebSocket URL: ${wsUrl.replace(encodedApiKey, '***API_KEY_HIDDEN***')}`);

                ws = new WebSocket(wsUrl, {
                    headers: {
                        'User-Agent': 'VoiceHype-VSCode-Extension',
                        'Origin': 'https://supabase.voicehype.ai'
                    },
                    // Increase timeout for better compatibility
                    handshakeTimeout: 20000
                });

                ws.on('open', () => {
                    console.log('VoiceHype: WebSocket connection opened');
                    this.emitStatus(TranscriptionStatus.WebSocketConnected);
                });

                ws.on('message', (data: Buffer) => {
                    try {
                        const message = data.toString();
                        console.log('VoiceHype: WebSocket message received:', message.substring(0, 200));

                        const parsedData = JSON.parse(message);

                        if (parsedData.type === 'connected') {
                            console.log('VoiceHype: Real-time transcription session established:', parsedData.sessionId);
                            this.emitStatus(TranscriptionStatus.SessionEstablished,
                                `Session ID: ${parsedData.sessionId}`);
                            isConnected = true;
                            // Now that we're connected, start streaming audio
                            streamAudioData();
                        } else if (parsedData.type === 'final') {
                            // For OpenAI responses through Edge Function
                            if (parsedData.text) {
                                console.log(`VoiceHype: Received final transcript:`, parsedData.text);
                                this.emitStatus(TranscriptionStatus.TranscriptionReceived, parsedData.text);
                                transcript += parsedData.text + ' ';
                            }
                        } else if (parsedData.message_type === 'CompleteTranscript') {
                            // For complete transcript from AssemblyAI at the end of session
                            if (parsedData.text) {
                                console.log(`VoiceHype: Received complete transcript:`, parsedData.text);
                                this.emitStatus(TranscriptionStatus.TranscriptionReceived, parsedData.text);
                                transcript = parsedData.text; // Replace with complete transcript
                            }
                        } else if (parsedData.type === 'partial') {
                            // For OpenAI partial responses
                            console.log(`VoiceHype: Received partial transcript:`, parsedData.text);
                            lastPartialTranscript = parsedData.text; // Update last partial
                        } else if (parsedData.message_type === 'FinalTranscript') {
                            // For AssemblyAI responses through Edge Function
                            console.log(`VoiceHype: Received final transcript from AssemblyAI:`, parsedData.text);
                            this.emitStatus(TranscriptionStatus.TranscriptionReceived, parsedData.text);
                            transcript += parsedData.text + ' ';
                        } else if (parsedData.message_type === 'PartialTranscript') {
                            // Don't accumulate partial transcripts as they are for server-side processing only
                            console.log(`VoiceHype: Received partial transcript from AssemblyAI:`, parsedData.text);
                            // Store the last partial transcript as backup in case we never get a final
                            if (parsedData.text && parsedData.text.trim() !== '') {
                                lastPartialTranscript = parsedData.text;
                            }
                        } else if (parsedData.type === 'error') {
                            console.error('VoiceHype: Real-time transcription error:', parsedData.message);
                            this.emitStatus(TranscriptionStatus.Error, parsedData.message);
                        } else if (parsedData.type === 'timeout') {
                            console.log('VoiceHype: Real-time transcription timeout:', parsedData.message);
                            this.emitStatus(TranscriptionStatus.Error, `Timeout: ${parsedData.message}`);
                        } else if (parsedData.type === 'service_disconnected') {
                            console.log('VoiceHype: Real-time transcription service disconnected:', parsedData.reason);
                            this.emitStatus(TranscriptionStatus.Error,
                                `Service disconnected: ${parsedData.reason}`);
                            isConnected = false;
                        } else {
                            console.log('VoiceHype: Other message received:', parsedData);
                        }
                    } catch (error) {
                        console.error('VoiceHype: Error parsing WebSocket message:', error, 'Raw data:', data.toString().substring(0, 200));
                    }
                });

                ws.on('error', (error) => {
                    console.error('VoiceHype: WebSocket error:', error);
                    this.emitStatus(TranscriptionStatus.Error, `WebSocket error: ${error.message}`);
                    isConnected = false;
                    clearTimeout(connectionTimeout);
                    reject(error);
                });

                ws.on('close', (code, reason) => {
                    console.log(`VoiceHype: WebSocket connection closed with code ${code}, reason: ${reason || 'None provided'}`);

                    // If we have no final transcript but have partial ones, use the last partial
                    if (transcript.trim() === '' && lastPartialTranscript.trim() !== '') {
                        console.log(`VoiceHype: No final transcript received, using last partial: "${lastPartialTranscript}"`);
                        transcript = lastPartialTranscript;
                    }

                    console.log(`VoiceHype: Final transcript: "${transcript}"`);
                    isConnected = false;
                    clearTimeout(connectionTimeout);
                    this.emitStatus(TranscriptionStatus.TranscriptionComplete);
                    resolve(transcript.trim());
                });
            };

            // Function to stream audio data in small chunks
            const streamAudioData = () => {
                if (!ws || !isConnected) {
                    console.log('VoiceHype: Cannot stream audio: WebSocket not connected');
                    return;
                }

                this.emitStatus(TranscriptionStatus.StreamingAudio);

                // Send audio data in chunks with delays to simulate streaming
                const chunkSize = 3200; // 100ms of 16kHz 16-bit audio
                const audioArray = new Uint8Array(audioData);

                console.log(`VoiceHype: Streaming audio in ${Math.ceil(audioArray.length / chunkSize)} chunks of ${chunkSize} bytes each`);

                // Function to send chunks with delay
                const sendChunk = async (index: number) => {
                    if (!ws || !isConnected || ws.readyState !== WebSocket.OPEN) {
                        console.log('VoiceHype: Stopping audio streaming: WebSocket disconnected');
                        return;
                    }

                    if (index >= audioArray.length) {
                        console.log('VoiceHype: Finished streaming audio data');

                        // Send close message after a delay to ensure all transcription comes back
                        setTimeout(() => {
                            if (ws && ws.readyState === WebSocket.OPEN) {
                                console.log('VoiceHype: Sending close message to server');
                                ws.send(JSON.stringify({ type: 'close' }));
                            }
                        }, 2000);

                        return;
                    }

                    const end = Math.min(index + chunkSize, audioArray.length);
                    const chunk = audioArray.slice(index, end);

                    try {
                        // Make sure the WebSocket is still open
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            // Ensure we have a valid buffer to send
                            const buffer = Buffer.from(chunk);
                            if (buffer && buffer.length > 0) {
                                ws.send(buffer);

                                // Log progress occasionally
                                if (index % (chunkSize * 10) === 0 || index + chunkSize >= audioArray.length) {
                                    const progress = Math.round((index / audioArray.length) * 100);
                                    console.log(`VoiceHype: Sent ${progress}% of audio data`);
                                }

                                // Delay between chunks to simulate real-time streaming
                                setTimeout(() => sendChunk(index + chunkSize), 100);
                            } else {
                                console.error('VoiceHype: Invalid audio chunk, skipping');
                                setTimeout(() => sendChunk(index + chunkSize), 100);
                            }
                        } else {
                            console.log('VoiceHype: WebSocket no longer open, stopping audio streaming');
                        }
                    } catch (error) {
                        console.error('VoiceHype: Error sending audio chunk:', error);
                        this.emitStatus(TranscriptionStatus.Error, `Error sending audio: ${error instanceof Error ? error.message : String(error)}`);
                    }
                };

                // Start sending chunks
                sendChunk(0);
            };

            // Start WebSocket connection
            connectWebSocket();
        });
    }

    // New method to set up real-time transcription
    public setupRealTimeConnection(): RealtimeConnectionManager {
        const service = this.configService.getTranscriptionService();
        const model = this.configService.getTranscriptionModel();
        const language = this.configService.getTranscriptionLanguage();
        const apiKey = this.configService.getApiKey();

        if (!apiKey) {
            throw new Error('API key not set');
        }

        // Get the current audio settings to determine sample rate
        const audioSettings = this.configService.getAudioSettings();
        const sampleRate = audioSettings.sampleRate || 16000;

        console.log(`VoiceHype: Setting up real-time connection with sample rate: ${sampleRate}Hz`);

        return new RealtimeConnectionManager(apiKey, service, model, language, sampleRate);
    }

    /**
     * Gets audio duration in seconds from the audio file
     * @param audioFilePath Path to the audio file
     * @returns Promise that resolves to duration in seconds
     */
    public async getAudioDuration(audioFilePath: string): Promise<number> {
        try {
            console.log(`VoiceHype: Getting audio duration for file: ${audioFilePath}`);

            // Check if the file exists
            if (!fs.existsSync(audioFilePath)) {
                console.error(`VoiceHype: Audio file does not exist: ${audioFilePath}`);
                return 0;
            }

            // Use WAV file header to get audio duration
            try {
                // Read the first 44 bytes of the WAV file (header)
                const fd = fs.openSync(audioFilePath, 'r');
                const buffer = Buffer.alloc(44);
                fs.readSync(fd, buffer, 0, 44, 0);
                fs.closeSync(fd);

                // WAV header format:
                // Offset 22-23: Number of channels (2 bytes, typically 1 or 2)
                const numChannels = buffer.readUInt16LE(22);

                // Offset 24-27: Sample rate (4 bytes)
                const sampleRate = buffer.readUInt32LE(24);

                // Offset 34-35: Bits per sample (2 bytes, typically 8, 16, 24, or 32)
                const bitsPerSample = buffer.readUInt16LE(34);

                // Get the file size
                const stats = fs.statSync(audioFilePath);
                const fileSize = stats.size;

                // Calculate bytes per sample
                const bytesPerSample = bitsPerSample / 8;

                // Calculate data size (total file size minus 44 byte header)
                const dataSize = fileSize - 44;

                // Calculate duration in seconds
                // Duration = dataSize / (sampleRate * numChannels * bytesPerSample)
                const duration = dataSize / (sampleRate * numChannels * bytesPerSample);

                console.log(`VoiceHype: Audio file details:`, {
                    numChannels,
                    sampleRate,
                    bitsPerSample,
                    fileSize,
                    dataSize,
                    duration
                });

                return duration;
            } catch (error) {
                console.error('VoiceHype: Error reading WAV header:', error);

                // Fallback to simple file size estimation
                try {
                    const stats = fs.statSync(audioFilePath);
                    const fileSize = stats.size;
                    // WAV files with 16-bit stereo at 44.1kHz use 176400 bytes per second (44100 * 2 channels * 2 bytes per sample)
                    // This is a rough estimation, but it works in many cases
                    const estimatedDuration = (fileSize - 44) / 176400;  // Subtract 44 bytes for WAV header
                    console.log(`VoiceHype: Estimated duration from file size: ${estimatedDuration.toFixed(3)} seconds`);
                    return estimatedDuration;
                } catch (estimationError) {
                    console.error('VoiceHype: Duration estimation failed:', estimationError);
                }
            }

            return 0;
        } catch (error) {
            console.error('VoiceHype: Error getting audio metadata:', error);
            // Return 0 in case of error
            return 0;
        }
    }
}
