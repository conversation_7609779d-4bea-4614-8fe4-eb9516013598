(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{12:function(e,t,a){},13:function(e,t,a){},14:function(e,t,a){"use strict";a.r(t);var o=a(0),n=a.n(o),r=a(3),i=(a(12),a(13),a(16)),s=a(17),l=a(18),c=a(19),d=a(20);var m=e=>{let{isRecording:t,isPaused:a,elapsedTime:o,onStart:r,onStop:m,onPause:u,onResume:p,onCancel:g}=e;return n.a.createElement("div",{className:"flex flex-col space-y-4"},n.a.createElement("div",{className:"flex items-center justify-center"},n.a.createElement("div",{className:"bg-surface dark:bg-dark-surface/80 border-primary/30 dark:border-primary/30 p-3 font-mono text-3xl border rounded-lg shadow-inner"},(e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`})(o))),n.a.createElement("div",{className:"flex items-center justify-center space-x-4"},t?n.a.createElement(n.a.Fragment,null,n.a.createElement("button",{className:"bg-primary hover:bg-primary-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Pause/Resume button clicked, current state:",{isRecording:t,isPaused:a}),a?(console.log("[RecordingControls] [DEBUG] Resuming recording"),p()):(console.log("[RecordingControls] [DEBUG] Pausing recording"),u())},"aria-label":a?"Resume recording":"Pause recording"},a?n.a.createElement(s.a,{className:"w-5 h-5"}):n.a.createElement(l.a,{className:"w-5 h-5"})),n.a.createElement("button",{className:"bg-secondary hover:bg-secondary-hover flex items-center justify-center w-12 h-12 text-white transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Stop button clicked, current state:",{isRecording:t,isPaused:a}),m()},"aria-label":"Stop recording"},n.a.createElement(c.a,{className:"w-5 h-5"})),n.a.createElement("button",{className:"bg-accent hover:bg-accent-hover flex items-center justify-center w-12 h-12 text-black transition-colors rounded-full shadow-md",onClick:g,"aria-label":"Cancel recording"},n.a.createElement(d.a,{className:"w-5 h-5"}))):n.a.createElement("button",{className:"w-14 h-14 bg-primary hover:bg-primary-hover flex items-center justify-center text-black transition-colors rounded-full shadow-md",onClick:()=>{console.log("[RecordingControls] [DEBUG] Start button clicked, current state:",{isRecording:t,isPaused:a}),r()},"aria-label":"Start recording"},n.a.createElement(i.a,{className:"w-6 h-6"}))))};const u=Object(o.memo)(e=>{let{service:t,onChange:a}=e;const r=Object(o.useCallback)(e=>{t!==e&&a(e)},[t,a]);return n.a.createElement("div",{className:"flex gap-2",role:"radiogroup","aria-label":"Select transcription service"},[{id:"assemblyai",name:"AssemblyAI"},{id:"openai",name:"Whisper"}].map(e=>n.a.createElement("button",{key:e.id,className:`flex-1 py-2.5 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50\n            ${t===e.id?"bg-primary dark:bg-dark-primary text-black shadow-sm font-bold":"bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 font-medium"}`,onClick:()=>r(e.id),role:"radio","aria-checked":t===e.id},e.name)))});u.displayName="ServiceSelector";var p=u,g=a(21);const b=Object(o.memo)(e=>{let{service:t,model:a,onChange:r}=e;const i=Object(o.useMemo)(()=>"assemblyai"===t?[{id:"best",name:"best",description:"High accuracy, optimized for various accents and domains"},{id:"nano",name:"nano",description:"Faster transcription with small quality tradeoff"}]:"openai"===t?[{id:"whisper-1",name:"whisper-1",description:"High-quality speech recognition model"}]:[],[t]),s=Object(o.useMemo)(()=>i.find(e=>e.id===a),[i,a]),l=Object(o.useCallback)(e=>{const t=e.target.value;t!==a&&r(t)},[a,r]);return"openai"===t?("whisper-1"!==a&&r("whisper-1"),n.a.createElement("div",{className:"space-y-3"})):n.a.createElement("div",{className:"space-y-3"},n.a.createElement("div",{className:"relative"},n.a.createElement("select",{className:"w-full px-3 py-2 appearance-none rounded-md border border-border dark:border-dark-border bg-white dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 cursor-pointer",value:a,onChange:l,"aria-label":"Select model"},i.map(e=>n.a.createElement("option",{key:e.id,value:e.id},e.name))),n.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},n.a.createElement("svg",{className:"h-4 w-4 text-muted dark:text-dark-muted",fill:"none",viewBox:"0 0 20 20",stroke:"currentColor"},n.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7l3-3 3 3m0 6l-3 3-3-3"})))),(null===s||void 0===s?void 0:s.description)&&n.a.createElement("div",{className:"flex items-start text-sm text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 p-3 rounded-md"},n.a.createElement(g.a,{className:"w-5 h-5 mr-2 flex-shrink-0 text-primary dark:text-dark-primary"}),n.a.createElement("p",null,s.description)))});b.displayName="ModelSelector";var h=b,k=a(22);const v=Object(o.memo)(e=>{let{service:t,model:a,language:r,onChange:i}=e;const s=Object(o.useMemo)(()=>{const e=[{code:"en",name:"Global English"},{code:"en_au",name:"Australian English"},{code:"en_uk",name:"British English"},{code:"en_us",name:"US English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"},{code:"nl",name:"Dutch"},{code:"hi",name:"Hindi"},{code:"ja",name:"Japanese"},{code:"zh",name:"Chinese"},{code:"fi",name:"Finnish"},{code:"ko",name:"Korean"},{code:"pl",name:"Polish"},{code:"ru",name:"Russian"},{code:"tr",name:"Turkish"},{code:"uk",name:"Ukrainian"},{code:"vi",name:"Vietnamese"}],o=[{code:"en",name:"Global English"},{code:"en_au",name:"Australian English"},{code:"en_uk",name:"British English"},{code:"en_us",name:"American English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"},{code:"nl",name:"Dutch"},{code:"af",name:"Afrikaans"},{code:"sq",name:"Albanian"},{code:"am",name:"Amharic"},{code:"ar",name:"Arabic"},{code:"hy",name:"Armenian"},{code:"as",name:"Assamese"},{code:"az",name:"Azerbaijani"},{code:"ba",name:"Bashkir"},{code:"eu",name:"Basque"},{code:"be",name:"Belarusian"},{code:"bn",name:"Bengali"},{code:"bs",name:"Bosnian"},{code:"br",name:"Breton"},{code:"bg",name:"Bulgarian"},{code:"my",name:"Burmese"},{code:"ca",name:"Catalan"},{code:"zh",name:"Chinese"},{code:"hr",name:"Croatian"},{code:"cs",name:"Czech"},{code:"da",name:"Danish"},{code:"et",name:"Estonian"},{code:"fo",name:"Faroese"},{code:"fi",name:"Finnish"},{code:"gl",name:"Galician"},{code:"ka",name:"Georgian"},{code:"el",name:"Greek"},{code:"gu",name:"Gujarati"},{code:"ht",name:"Haitian"},{code:"ha",name:"Hausa"},{code:"haw",name:"Hawaiian"},{code:"he",name:"Hebrew"},{code:"hi",name:"Hindi"},{code:"hu",name:"Hungarian"},{code:"is",name:"Icelandic"},{code:"id",name:"Indonesian"},{code:"ja",name:"Japanese"},{code:"jv",name:"Javanese"},{code:"kn",name:"Kannada"},{code:"kk",name:"Kazakh"},{code:"km",name:"Khmer"},{code:"ko",name:"Korean"},{code:"lo",name:"Lao"},{code:"la",name:"Latin"},{code:"lv",name:"Latvian"},{code:"ln",name:"Lingala"},{code:"lt",name:"Lithuanian"},{code:"lb",name:"Luxembourgish"},{code:"mk",name:"Macedonian"},{code:"mg",name:"Malagasy"},{code:"ms",name:"Malay"},{code:"ml",name:"Malayalam"},{code:"mt",name:"Maltese"},{code:"mi",name:"Maori"},{code:"mr",name:"Marathi"},{code:"mn",name:"Mongolian"},{code:"ne",name:"Nepali"},{code:"no",name:"Norwegian"},{code:"nn",name:"Norwegian Nynorsk"},{code:"oc",name:"Occitan"},{code:"pa",name:"Panjabi"},{code:"ps",name:"Pashto"},{code:"fa",name:"Persian"},{code:"pl",name:"Polish"},{code:"ro",name:"Romanian"},{code:"ru",name:"Russian"},{code:"sa",name:"Sanskrit"},{code:"sr",name:"Serbian"},{code:"sn",name:"Shona"},{code:"sd",name:"Sindhi"},{code:"si",name:"Sinhala"},{code:"sk",name:"Slovak"},{code:"sl",name:"Slovenian"},{code:"so",name:"Somali"},{code:"su",name:"Sundanese"},{code:"sw",name:"Swahili"},{code:"sv",name:"Swedish"},{code:"tl",name:"Tagalog"},{code:"tg",name:"Tajik"},{code:"ta",name:"Tamil"},{code:"tt",name:"Tatar"},{code:"te",name:"Telugu"},{code:"th",name:"Thai"},{code:"bo",name:"Tibetan"},{code:"tr",name:"Turkish"},{code:"tk",name:"Turkmen"},{code:"uk",name:"Ukrainian"},{code:"ur",name:"Urdu"},{code:"uz",name:"Uzbek"},{code:"vi",name:"Vietnamese"},{code:"cy",name:"Welsh"},{code:"yi",name:"Yiddish"},{code:"yo",name:"Yoruba"}],n=[{code:"af",name:"Afrikaans"},{code:"ar",name:"Arabic"},{code:"hy",name:"Armenian"},{code:"az",name:"Azerbaijani"},{code:"be",name:"Belarusian"},{code:"bs",name:"Bosnian"},{code:"bg",name:"Bulgarian"},{code:"ca",name:"Catalan"},{code:"zh",name:"Chinese"},{code:"hr",name:"Croatian"},{code:"cs",name:"Czech"},{code:"da",name:"Danish"},{code:"nl",name:"Dutch"},{code:"en",name:"English"},{code:"et",name:"Estonian"},{code:"fi",name:"Finnish"},{code:"fr",name:"French"},{code:"gl",name:"Galician"},{code:"de",name:"German"},{code:"el",name:"Greek"},{code:"he",name:"Hebrew"},{code:"hi",name:"Hindi"},{code:"hu",name:"Hungarian"},{code:"is",name:"Icelandic"},{code:"id",name:"Indonesian"},{code:"it",name:"Italian"},{code:"ja",name:"Japanese"},{code:"kn",name:"Kannada"},{code:"kk",name:"Kazakh"},{code:"ko",name:"Korean"},{code:"lv",name:"Latvian"},{code:"lt",name:"Lithuanian"},{code:"mk",name:"Macedonian"},{code:"ms",name:"Malay"},{code:"mr",name:"Marathi"},{code:"mi",name:"Maori"},{code:"ne",name:"Nepali"},{code:"no",name:"Norwegian"},{code:"fa",name:"Persian"},{code:"pl",name:"Polish"},{code:"pt",name:"Portuguese"},{code:"ro",name:"Romanian"},{code:"ru",name:"Russian"},{code:"sr",name:"Serbian"},{code:"sk",name:"Slovak"},{code:"sl",name:"Slovenian"},{code:"es",name:"Spanish"},{code:"sw",name:"Swahili"},{code:"sv",name:"Swedish"},{code:"tl",name:"Tagalog"},{code:"ta",name:"Tamil"},{code:"th",name:"Thai"},{code:"tr",name:"Turkish"},{code:"uk",name:"Ukrainian"},{code:"ur",name:"Urdu"},{code:"vi",name:"Vietnamese"},{code:"cy",name:"Welsh"}];return"whisper"===t||"openai"===t?n.sort((e,t)=>e.name.localeCompare(t.name)):"assemblyai"===t?"best"===a?e.sort((e,t)=>e.name.localeCompare(t.name)):o.sort((e,t)=>e.name.localeCompare(t.name)):n.sort((e,t)=>e.name.localeCompare(t.name))},[t,a]),l=Object(o.useCallback)(e=>{const t=e.target.value;t!==r&&i(t)},[r,i]);return n.a.createElement("div",{className:"relative mb-4"},n.a.createElement("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},n.a.createElement(k.a,{className:"text-muted dark:text-dark-muted w-5 h-5"})),n.a.createElement("select",{className:"border-border dark:border-dark-border dark:bg-dark-surface text-text dark:text-dark-text  focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50  w-full py-2.5 pl-10 pr-8 bg-white border rounded-lg appearance-none cursor-pointer transition-colors duration-200 ease-in-out hover:border-primary/70 dark:hover:border-dark-primary/70",value:r,onChange:l,"aria-label":"Select language"},s.map(e=>n.a.createElement("option",{key:e.code,value:e.code,className:"py-1.5 px-2 hover:bg-primary/10 dark:hover:bg-dark-primary/10"},e.name))),n.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},n.a.createElement("svg",{className:"text-muted dark:text-dark-muted w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},n.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"}))))});v.displayName="LanguageSelector";var f=v;const y=Object(o.memo)(e=>{let{optimizationModel:t,onChange:a}=e;const r=Object(o.useMemo)(()=>[{id:"llama-4-scout",name:"Llama 4 Scout",description:"Meta's latest large language model with excellent reasoning",category:"llama"},{id:"llama-4-maverick",name:"Llama 4 Maverick",description:"Meta's latest large language model with advanced capabilities",category:"llama"},{id:"llama-3-70b",name:"Llama 3 70B",description:"Meta's 70B parameter model with strong performance",category:"llama"},{id:"llama-3-8b",name:"Llama 3 8B",description:"Meta's 8B parameter model optimized for efficiency",category:"llama"},{id:"claude-3.7-sonnet",name:"Claude 3.7 Sonnet",description:"Anthropic's latest model with excellent reasoning and instruction following",category:"claude"},{id:"claude-3.5-sonnet",name:"Claude 3.5 Sonnet",description:"Anthropic's balanced model with good performance",category:"claude"},{id:"claude-3.5-haiku",name:"Claude 3.5 Haiku",description:"Anthropic's fast model optimized for efficiency",category:"claude"},{id:"deepseek-r1",name:"DeepSeek R1",description:"DeepSeek's reasoning model with strong analytical capabilities",category:"deepseek"},{id:"deepseek-v3",name:"DeepSeek V3",description:"DeepSeek's latest model with improved performance",category:"deepseek"}],[]),i=Object(o.useMemo)(()=>r.find(e=>e.id===t)||r[0],[r,t]),s=Object(o.useCallback)(e=>{const o=e.target.value;o!==t&&a(o)},[t,a]),l=Object(o.useMemo)(()=>{return{llamaModels:r.filter(e=>"llama"===e.category),claudeModels:r.filter(e=>"claude"===e.category),deepseekModels:r.filter(e=>"deepseek"===e.category)}},[r]);return n.a.createElement("div",{className:"space-y-3"},n.a.createElement("div",{className:"relative"},n.a.createElement("select",{className:"w-full px-3 py-2 appearance-none rounded-md border border-border dark:border-dark-border bg-white dark:bg-dark-surface text-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 cursor-pointer",value:t,onChange:s,"aria-label":"Select optimization model"},n.a.createElement("optgroup",{label:"Llama Models"},l.llamaModels.map(e=>n.a.createElement("option",{key:e.id,value:e.id},e.name))),n.a.createElement("optgroup",{label:"Claude Models"},l.claudeModels.map(e=>n.a.createElement("option",{key:e.id,value:e.id},e.name))),n.a.createElement("optgroup",{label:"DeepSeek Models"},l.deepseekModels.map(e=>n.a.createElement("option",{key:e.id,value:e.id},e.name)))),n.a.createElement("div",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},n.a.createElement("svg",{className:"h-4 w-4 text-muted dark:text-dark-muted",fill:"none",viewBox:"0 0 20 20",stroke:"currentColor"},n.a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7l3-3 3 3m0 6l-3 3-3-3"})))),(null===i||void 0===i?void 0:i.description)&&n.a.createElement("div",{className:"flex items-start text-sm text-muted dark:text-dark-muted bg-gray-50 dark:bg-dark-surface/40 p-3 rounded-md"},n.a.createElement(g.a,{className:"w-5 h-5 mr-2 flex-shrink-0 text-primary dark:text-dark-primary"}),n.a.createElement("p",null,i.description)))});y.displayName="OptimizationModelSelector";var w=y,x=a(23),E=a(24),N=a(25),C=a(26),S=a(27),M=a(28),O=a(29);const j=()=>"function"===typeof j?window.acquireVsCodeApi():{postMessage:e=>{console.log("Development mode: posting message to extension",e)},getState:()=>null,setState:e=>{console.log("Development mode: setting state",e)}},T=j();var $=e=>{let{transcriptions:t,onCopyOriginal:a,onCopyOptimized:n,onOptimize:r}=e;const[i,s]=Object(o.useState)(""),[l,c]=Object(o.useState)("newest"),[d,m]=Object(o.useState)(5),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return e?e.length<=t?e:e.substring(0,t)+"...":""},p=Object(o.useMemo)(()=>{let e=[...t];switch(i&&(e=e.filter(e=>e.originalText.toLowerCase().includes(i.toLowerCase())||e.optimizedText&&e.optimizedText.toLowerCase().includes(i.toLowerCase()))),l){case"newest":e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());break;case"oldest":e.sort((e,t)=>new Date(e.timestamp).getTime()-new Date(t.timestamp).getTime());break;default:e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())}return e},[t,i,l]),g=Object(o.useMemo)(()=>p.slice(0,d),[p,d]),b=g.length<p.length;return o.createElement("div",{className:"flex flex-col h-full"},o.createElement("div",{className:"border-border dark:border-dark-border p-2 mb-2 border-b"},o.createElement("div",{className:"flex items-center justify-between mb-2"},o.createElement("div",{className:"relative flex-grow"},o.createElement("input",{type:"text",placeholder:"Search transcriptions...",value:i,onChange:e=>s(e.target.value),className:"w-full px-3 py-1.5 pl-8 text-sm bg-gray-50 dark:bg-dark-surface border border-border dark:border-dark-border rounded-md"}),o.createElement(x.a,{className:"absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-muted dark:text-dark-muted"}),i&&o.createElement("button",{onClick:()=>s(""),className:"absolute right-2.5 top-1/2 transform -translate-y-1/2"},o.createElement(E.a,{className:"w-3.5 h-3.5 text-muted dark:text-dark-muted hover:text-primary dark:hover:text-dark-primary"}))),t.length>0&&o.createElement("button",{onClick:()=>{console.log("RecentTranscriptions: Clear all history button clicked. Current transcriptions count:",t.length),T.postMessage({command:"showConfirmation",message:"Are you sure you want to delete all transcription history? This cannot be undone.",onConfirm:"clearTranscriptionHistory"}),console.log("RecentTranscriptions: Sent showConfirmation message with onConfirm=clearTranscriptionHistory")},className:"ml-2 p-1.5 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md",title:"Clear all transcription history"},o.createElement(N.a,{className:"w-4 h-4"}))),o.createElement("div",{className:"flex items-center justify-between"},o.createElement("div",{className:"flex items-center justify-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-full p-1 max-w-[200px]"},o.createElement("button",{className:`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${"newest"===l?"bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary":"text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"}`,onClick:()=>c("newest")},"Newest"),o.createElement("button",{className:`flex-1 py-1 px-3 text-xs font-medium rounded-full transition-colors ${"oldest"===l?"bg-white dark:bg-gray-700 shadow-sm text-primary dark:text-dark-primary":"text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"}`,onClick:()=>c("oldest")},"Oldest")),t.length>0&&o.createElement("span",{className:"text-muted dark:text-dark-muted text-xs"},t.length,"/50 messages"))),o.createElement("div",{className:"flex-grow px-2 pb-2 space-y-3 overflow-y-auto"},0===p.length?o.createElement("div",{className:"text-muted dark:text-dark-muted border-border dark:border-dark-border flex flex-col items-center justify-center p-4 text-sm text-center border border-dashed rounded-md"},o.createElement(C.a,{className:"w-8 h-8 mb-2 opacity-50"}),i?"No matching transcriptions found":"No transcription history yet"):o.createElement(o.Fragment,null,g.map(e=>o.createElement("div",{key:e.id,className:"dark:bg-dark-surface/50 border-border dark:border-dark-border group overflow-hidden bg-white border rounded-md shadow-sm"},o.createElement("div",{className:"flex items-center justify-between px-3 py-1.5 bg-gray-50 dark:bg-dark-surface border-b border-border dark:border-dark-border"},o.createElement("div",{className:"text-muted dark:text-dark-muted flex items-center text-xs"},o.createElement(C.a,{className:"w-3 h-3 mr-1"}),o.createElement("span",null,(e=>{try{const a=new Date(e),o=new Date;return a.getDate()===o.getDate()&&a.getMonth()===o.getMonth()&&a.getFullYear()===o.getFullYear()?a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):a.toLocaleDateString([],{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(t){return console.error("Error formatting timestamp:",t),"Invalid date"}})(e.timestamp)),e.duration&&o.createElement("span",{className:"ml-2"},"(",(e=>e?`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`:"")(e.duration),")")),o.createElement("div",{className:"flex items-center"},o.createElement("div",{className:"text-muted dark:text-dark-muted mr-2 text-xs"},((e,t)=>`${e||"unknown"} - ${t||"unknown"}`)(e.service,e.model)),o.createElement("button",{onClick:()=>(e=>{T.postMessage({command:"deleteTranscription",id:e})})(e.id),className:"group-hover:opacity-100 text-muted hover:text-red-500 dark:text-dark-muted dark:hover:text-red-400 transition-opacity opacity-0",title:"Delete transcription"},o.createElement(N.a,{className:"w-3.5 h-3.5"})))),o.createElement("div",{className:"p-2 space-y-2"},o.createElement("div",{className:"space-y-1"},o.createElement("div",{className:"text-muted dark:text-dark-muted text-xs font-semibold uppercase"},"Original"),o.createElement("div",{className:"flex"},o.createElement("div",{className:"flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs"},u(e.originalText)),o.createElement("button",{className:"text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1",onClick:()=>a(e.id),title:"Copy original transcript"},o.createElement(S.a,{className:"w-4 h-4"})))),e.optimizedText?o.createElement("div",{className:"space-y-1"},o.createElement("div",{className:"text-accent dark:text-dark-accent text-xs font-semibold uppercase"},"Optimized"),o.createElement("div",{className:"flex"},o.createElement("div",{className:"flex-grow p-1.5 bg-gray-50 dark:bg-dark-surface/50 rounded text-xs"},u(e.optimizedText)),o.createElement("button",{className:"text-muted hover:text-primary dark:text-dark-muted dark:hover:text-dark-primary p-1 ml-1",onClick:()=>n(e.id),title:"Copy optimized transcript"},o.createElement(S.a,{className:"w-4 h-4"})))):o.createElement("div",{className:"flex justify-end"},o.createElement("button",{className:"bg-primary/10 hover:bg-primary/20 dark:bg-dark-primary/20 dark:hover:bg-dark-primary/30 text-primary dark:text-dark-primary flex items-center px-2 py-1 text-xs font-medium rounded",onClick:()=>r(e.id),title:"Optimize this transcript"},o.createElement(M.a,{className:"w-3 h-3 mr-1"}),"Optimize"))))),b&&o.createElement("div",{className:"flex justify-center pt-2"},o.createElement("button",{onClick:()=>{m(e=>e+5)},className:"flex items-center px-4 py-1.5 text-xs font-medium text-primary dark:text-dark-primary border border-primary/30 dark:border-dark-primary/30 hover:bg-primary/5 dark:hover:bg-dark-primary/10 rounded-md"},o.createElement(O.a,{className:"w-3.5 h-3.5 mr-1.5"}),"Show More (",p.length-g.length," remaining)")))))};var D=n.a.memo(e=>{let{label:t,checked:a,onChange:o,className:r=""}=e;return n.a.createElement("div",{className:`flex items-center justify-between p-3 mb-3 bg-white dark:bg-dark-surface border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:border-primary/70 dark:hover:border-primary/70 transition-colors duration-200 ${r}`},n.a.createElement("label",{className:"flex items-center justify-between w-full cursor-pointer"},n.a.createElement("span",{className:"dark:text-white text-sm font-medium text-gray-900"},t),n.a.createElement("div",{className:"relative ml-auto"},n.a.createElement("input",{type:"checkbox",className:"sr-only",checked:a,onChange:e=>o(e.target.checked)}),n.a.createElement("div",{className:`block w-10 h-6 rounded-full transition-colors duration-200 ease-in-out ${a?"bg-primary dark:bg-primary":"bg-gray-300 dark:bg-gray-700"}`}),n.a.createElement("div",{className:`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${a?"transform translate-x-4":"transform translate-x-0"}`}))))});var z=e=>{let{sampleRate:t,deviceId:a,onSampleRateChange:o,onDeviceChange:r}=e;return n.a.createElement("div",{className:"space-y-4"},n.a.createElement("div",null,n.a.createElement("label",{className:"text-text dark:text-dark-text block mb-2 text-sm font-medium"},"Audio Device"),n.a.createElement("input",{type:"text",value:a||"",onChange:e=>r(""===e.target.value?"":e.target.value),placeholder:"System Default (e.g. hw:1,0 for Linux)",className:"w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50"})),n.a.createElement("div",null,n.a.createElement("label",{className:"text-text dark:text-dark-text block mb-2 text-sm font-medium"},"Sample Rate"),n.a.createElement("div",{className:"relative"},n.a.createElement("select",{value:t,onChange:e=>o(Number(e.target.value)),className:"w-full px-4 py-2.5 bg-gray-100 dark:bg-dark-surface hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 dark:focus:ring-dark-primary/50 appearance-none"},[8e3,16e3,22050,44100,48e3].map(e=>n.a.createElement("option",{key:e,value:e},e.toLocaleString()," Hz"))),n.a.createElement("div",{className:"dark:text-gray-200 absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 pointer-events-none"},n.a.createElement("svg",{className:"w-4 h-4 fill-current",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},n.a.createElement("path",{d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"}))))))};var R=e=>{let{className:t=""}=e;const[a,r]=Object(o.useState)(!1);Object(o.useEffect)(()=>{const e=()=>{const e=document.body.classList.contains("vscode-dark")||document.documentElement.classList.contains("vscode-dark");r(e)};e();const t=new MutationObserver(e);return t.observe(document.body,{attributes:!0,attributeFilter:["class"]}),t.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>t.disconnect()},[]);const i=a?window.voicehypeLogoLight:window.voicehypeLogoDark;return console.log(`Using ${a?"light":"dark"} logo:`,i),n.a.createElement("div",{className:`logo-container ${t}`},i?n.a.createElement("img",{src:i,alt:"VoiceHype Logo",className:"logo-image"}):n.a.createElement("div",{className:"logo-placeholder"},"VoiceHype"))};var P=e=>{let{children:t,onClick:a,variant:o="primary",size:r="md",disabled:i=!1}=e;return n.a.createElement("button",{onClick:a,disabled:i,className:`rounded font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1 ${{sm:"px-2 py-1 text-sm",md:"px-3 py-1.5 text-base",lg:"px-4 py-2 text-lg"}[r]} ${{primary:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 focus:ring-blue-500",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 dark:border dark:border-gray-600 focus:ring-gray-500"}[o]} ${i?"opacity-50 cursor-not-allowed":""}`},t)};const U={id:"custom",name:"Custom",prompt:"",isCustom:!0},L=[{id:"clean-up",name:"Clean Up",prompt:'Clean up the text by removing filler words like "um", "you know", "like", fix any grammar mistakes, and make the transcript coherent and smooth, while preserving the original meaning. Return ONLY the cleaned text without any additional commentary or conversational responses.'},{id:"translate",name:"Translate",prompt:"Translate the following transcript into [TARGET_LANGUAGE], keeping it natural and respectful of tone and cultural context. Do not summarize, just translate as clearly as possible."},{id:"summarize",name:"Summarize",prompt:"Summarize the following transcript into a short, coherent paragraph that captures the key ideas, tone, and intention of the speaker. Return ONLY the summarized text without any introductory phrases or additional commentary."},{id:"polish",name:"Polish",prompt:"Rewrite the following transcript to make it sound formal and professional, correcting grammar and sentence structure, while preserving the speaker's message."},{id:"expand",name:"Expand",prompt:"Expand and elaborate the transcript into a well-written, detailed explanation or article, adding transitions and clarifications where needed, while staying faithful to the speaker's intent."},{id:"light-touch",name:"Light Touch",prompt:"Apply minimal editing to fix major grammatical issues and incoherence, but keep the casual tone and natural flow of the original speech."},{id:"bullet-points",name:"Bullet Points",prompt:"Convert the following transcript into a clear, concise list of bullet points summarizing the main ideas discussed."},{id:"islamic-tone",name:"Islamic Tone",prompt:'Transform this transcript into an uplifting Islamic motivational message. Use Quranic verses and Hadith to inspire positive action while maintaining the original meaning. Include motivational Islamic phrases like "With Allah\'s help we can achieve", "Allah has promised us", "Let\'s strive to be better Muslims", and "This is our chance to earn Allah\'s pleasure". Keep the tone encouraging, hopeful and spiritually uplifting while preserving the original content\'s essence. The goal is to motivate the listener/reader to take positive action in their life while strengthening their faith.'},{id:"whatsapp-style",name:"WhatsApp Style",prompt:"Rewrite the following text as a short, casual WhatsApp message. Keep it friendly and easy to understand."},{id:"email-format",name:"Email Format",prompt:"Rewrite the following transcript into a professional email. Include a greeting, body, and closing, and keep the tone polite and formal."},{id:"reminder-note",name:"Reminder Note",prompt:"Convert the following transcript into a short personal reminder, using brief and clear language."},{id:"to-do-list",name:"To-Do List",prompt:"Extract a to-do list from the following transcript. Format it as simple, actionable tasks in bullet points."},{id:"meeting-minutes",name:"Meeting Minutes",prompt:"Format the following text into structured meeting minutes, listing topics discussed, decisions made, and action items clearly."},{id:"social-caption",name:"Social Caption",prompt:"Rewrite the text into a short, engaging social media caption suitable for Instagram, Twitter, or Facebook, keeping the tone casual and inviting."}];var A=e=>{let{value:t,onChange:a,onModeChange:r}=e;const[i,s]=Object(o.useState)([...L,U]),[l,c]=Object(o.useState)("clean-up"),[d,m]=Object(o.useState)(!1),[u,p]=Object(o.useState)(""),[g,b]=Object(o.useState)(""),[h,k]=Object(o.useState)("English"),[v,f]=Object(o.useState)("English");Object(o.useEffect)(()=>{const e=setTimeout(()=>{f(h)},500);return()=>{clearTimeout(e)}},[h]),Object(o.useEffect)(()=>{console.log("PromptModeSelector: Requesting prompt modes with default activeMode:",l),T.postMessage({command:"getPromptModes",persist:!0});const e=e=>{if("promptModes"===e.data.command){const t=e.data.modes||[];console.log(`PromptModeSelector: Received ${t.length} custom modes from extension`),s([...L,...t,U]),e.data.activeMode?(console.log("PromptModeSelector: Setting active mode from configuration:",e.data.activeMode),c(e.data.activeMode)):console.log("PromptModeSelector: No active mode provided, keeping default:",l)}else"initialOptions"===e.data.command?(e.data.activePromptMode&&(console.log("PromptModeSelector: Setting initial active mode from configuration:",e.data.activePromptMode),c(e.data.activePromptMode)),Array.isArray(e.data.customPromptModes)&&e.data.customPromptModes.length>0&&(console.log(`PromptModeSelector: Setting ${e.data.customPromptModes.length} initial custom modes from configuration`),s([...L,...e.data.customPromptModes,U]))):"activePromptModeUpdated"===e.data.command&&e.data.modeId&&(console.log("PromptModeSelector: Active mode confirmed by extension:",e.data.modeId),c(e.data.modeId))};return window.addEventListener("message",e),()=>window.removeEventListener("message",e)},[]),Object(o.useEffect)(()=>{const e=i.find(e=>e.id===l);if(e&&"custom"!==e.id){let t=e.prompt;"translate"===e.id&&(t=t.replace("[TARGET_LANGUAGE]",v),a(t)),p(t)}},[l,i,v]);return n.a.createElement("div",{className:"space-y-3"},n.a.createElement("div",{className:"flex flex-wrap gap-2"},i.map(e=>n.a.createElement("div",{key:e.id,className:"flex items-center"},n.a.createElement("div",{onClick:()=>(e=>{c(e.id),"custom"!==e.id&&(a(e.prompt),p(e.prompt)),r&&r(e.id),m("custom"===e.id)})(e),className:`px-3 py-1.5 rounded-full border transition-colors cursor-pointer\n                ${l===e.id?"bg-primary dark:bg-primary border-primary dark:border-primary text-black font-bold":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}`},n.a.createElement("span",{className:"text-sm font-medium select-none"},e.name)),e.isCustom&&"custom"!==e.id&&n.a.createElement("button",{onClick:()=>{T.postMessage({command:"deletePromptMode",modeId:e.id}),s(i.filter(t=>t.id!==e.id)),l===e.id&&c("clean-up")},className:"hover:text-red-700 p-1 ml-1 text-red-500 rounded-full",title:"Delete custom mode"},n.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor"},n.a.createElement("path",{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})))))),"translate"===l&&n.a.createElement("div",{className:"mt-3"},n.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Target Language"),n.a.createElement("input",{type:"text",value:h,onChange:e=>k(e.target.value),placeholder:"Enter target language (e.g. French, Arabic)",className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"})),"custom"!==l&&!d&&u&&n.a.createElement("div",{className:"dark:border-gray-600 bg-gray-50 dark:bg-gray-800 p-3 mt-3 border border-gray-300 rounded-md"},n.a.createElement("p",{className:"dark:text-gray-300 text-sm text-gray-600"},u)),d&&n.a.createElement("div",{className:"mt-3"},n.a.createElement("div",{className:"mb-3"},n.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Mode Name"),n.a.createElement("input",{type:"text",value:g,onChange:e=>b(e.target.value),placeholder:"Enter a name for this mode",className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm"})),n.a.createElement("label",{className:"dark:text-gray-300 block mb-1 text-sm font-medium text-gray-700"},"Prompt Text"),n.a.createElement("textarea",{value:t,onChange:e=>{const t=e.target.value;p(t),a(t)},placeholder:"Enter custom prompt...",rows:4,className:"dark:border-gray-600 dark:bg-gray-700 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 w-full p-2 bg-white border border-gray-300 rounded-md shadow-sm resize-none"}),n.a.createElement("div",{className:"flex items-center justify-between mt-2"},n.a.createElement("div",{className:"dark:text-gray-400 text-sm text-gray-500"},"Create a custom optimization mode"),n.a.createElement(P,{size:"sm",onClick:()=>{if(t&&t.trim()){const e=g.trim()?g.trim():`Custom ${(new Date).toLocaleString()}`;T.postMessage({command:"savePromptMode",mode:{id:`custom-${Date.now()}`,name:e,prompt:u,isCustom:!0}}),b(""),a("")}},disabled:!t||!t.trim()},"Save Mode"))))};var V=()=>{const e=Object(o.useRef)({service:0,model:0,language:0,optimize:0,optimizationModel:0,translate:0,customPrompt:0,realtime:0,sampleRate:0,deviceId:0}),t=Object(o.useRef)({service:"",model:"",language:"",optimizeEnabled:!1,optimizationModel:"gpt-4o",translate:!1,realtime:!1,customPrompt:"",sampleRate:44100,deviceId:null,apiKey:""}),[a,n]=Object(o.useState)(!1),[r,i]=Object(o.useState)(!1),[s,l]=Object(o.useState)(!1),[c,d]=Object(o.useState)(0),[u,g]=Object(o.useState)("assemblyai"),[b,k]=Object(o.useState)("whisper-1"),[v,y]=Object(o.useState)("best"),[x,E]=Object(o.useState)("en"),[N,C]=Object(o.useState)("Correct any grammar issues and improve clarity. Keep the meaning intact."),[S,M]=Object(o.useState)(!0),[O,j]=Object(o.useState)("gpt-4o"),[P,U]=Object(o.useState)(!1),[L,V]=Object(o.useState)(!1),[W,I]=Object(o.useState)(22050),[B,G]=Object(o.useState)(null),[H,F]=Object(o.useState)([]),[K,q]=Object(o.useState)("");Object(o.useEffect)(()=>{t.current={service:u,model:b,language:x,optimizeEnabled:S,optimizationModel:O,translate:P,realtime:L,customPrompt:N,sampleRate:W,deviceId:B,apiKey:K}},[u,b,x,S,O,P,L,N,N.length,W,B,K]),Object(o.useEffect)(()=>{a&&(console.log("WebView: Requesting initial transcriptions"),T.postMessage({command:"getTranscriptions"}))},[a]);const[_,J]=Object(o.useState)([]),Y=Object(o.useCallback)((t,o,n)=>{const r=Date.now(),i=e.current[t];return a?r-i>1e3&&(e.current[t]=r,n(o),!0):(e.current[t]=r,n(o),!0)},[a]);Object(o.useEffect)(()=>{const o=o=>{const m=o.data;switch(m.command){case"transcription":J(e=>[{id:Date.now().toString(),timestamp:(new Date).toISOString(),originalText:m.text,optimizedText:m.optimizedText,service:t.current.service,model:t.current.model,language:t.current.language},...e]);break;case"initialConfiguration":if(m.options){console.log("Received initial configuration:",m.options);const{service:t,model:a,language:o,customPrompt:r,optimize:i,optimizationModel:s,translate:l,realtime:c,apiKey:d,audioDevice:p}=m.options;void 0!==t&&(console.log(`Setting service to: ${t} (was: ${u})`),g(t)),void 0!==a&&(console.log(`Setting model to: ${a} (was: ${b})`),k(a)),void 0!==o&&(console.log(`Setting language to: ${o} (was: ${x})`),E(o)),void 0!==r&&(console.log(`Setting custom prompt (length: ${r.length})`),C(r)),void 0!==i&&(console.log(`Setting optimize to: ${i} (was: ${S})`),M(i)),void 0!==l&&(console.log(`Setting translate to: ${l} (was: ${P})`),U(l)),void 0!==c&&(console.log(`Setting realtime to: ${c} (was: ${L})`),V(c)),void 0!==s&&(console.log(`Setting optimization model to: ${s} (was: ${O})`),j(s)),void 0!==d&&(console.log("Setting API key"),q(d)),void 0!==p&&(console.log("Setting audio device:",p),G(p)),"assemblyai"===t&&void 0!==a&&y(a),n(!0),Object.keys(e.current).forEach(t=>{e.current[t]=Date.now()}),setTimeout(()=>{console.log("WebView state after initialization:"),console.log(`Service: ${u}`),console.log(`Model: ${b}`),console.log(`Language: ${x}`),console.log(`Optimize: ${S}`),console.log(`Custom prompt length: ${N.length}`),console.log(`Translate: ${P}`),console.log(`Realtime: ${L}`)},100)}else console.warn("Received initialConfiguration message with no options");break;case"updateOptions":if(m.options){console.log("Received updateOptions:",m.options);const{service:t,model:o,language:r,customPrompt:i,optimize:s,optimizationModel:l,translate:c,realtime:d,audioDevice:p}=m.options;let h=!1;const v=!a||!0===m.force;console.log("Current state before updateOptions:"),console.log(`Service: ${u}, Model: ${b}, Language: ${x}`),console.log(`Optimize: ${S}, Translate: ${P}, Realtime: ${L}`),void 0!==t&&(v||Date.now()-e.current.service>1e3?t!==u&&(console.log(`Updating service from ${u} to ${t}`),g(t),e.current.service=Date.now(),h=!0):console.log(`Skipping service update (${t}) due to recent local update`)),void 0!==o&&(v||Date.now()-e.current.model>1e3?o!==b&&(console.log(`Updating model from ${b} to ${o}`),k(o),e.current.model=Date.now(),h=!0,"assemblyai"!==t&&"assemblyai"!==u||y(o)):console.log(`Skipping model update (${o}) due to recent local update`)),void 0!==r&&(v||Date.now()-e.current.language>1e3?r!==x&&(console.log(`Updating language from ${x} to ${r}`),E(r),e.current.language=Date.now(),h=!0):console.log(`Skipping language update (${r}) due to recent local update`)),void 0!==i&&(v||Date.now()-e.current.customPrompt>1e3?(console.log(`Updating custom prompt (length: ${i.length})`),C(i),e.current.customPrompt=Date.now(),h=!0):console.log("Skipping custom prompt update due to recent local update")),void 0!==s&&(v||Date.now()-e.current.optimize>1e3?s!==S&&(console.log(`Updating optimize from ${S} to ${s}`),M(s),e.current.optimize=Date.now(),h=!0):console.log(`Skipping optimize update (${s}) due to recent local update`)),void 0!==l&&(v||Date.now()-e.current.optimizationModel>1e3?l!==O&&(console.log(`Updating optimization model from ${O} to ${l}`),j(l),e.current.optimizationModel=Date.now(),h=!0):console.log(`Skipping optimization model update (${l}) due to recent local update`)),void 0!==c&&(v||Date.now()-e.current.translate>1e3?c!==P&&(console.log(`Updating translate from ${P} to ${c}`),U(c),e.current.translate=Date.now(),h=!0):console.log(`Skipping translate update (${c}) due to recent local update`)),void 0!==d&&(v||Date.now()-e.current.realtime>1e3?d!==L&&(console.log(`Updating realtime from ${L} to ${d}`),V(d),e.current.realtime=Date.now(),h=!0):console.log(`Skipping realtime update (${d}) due to recent local update`)),void 0!==p&&(v||Date.now()-e.current.deviceId>1e3?p!==B&&(console.log(`Updating audio device from ${B} to ${p}`),G(p),e.current.deviceId=Date.now(),h=!0):console.log(`Skipping audio device update (${p}) due to recent local update`)),!a&&h&&(console.log("Initial configuration loaded via updateOptions"),n(!0)),h?console.log("Configuration updated successfully"):console.log("No configuration changes applied")}else console.warn("Received updateOptions message with no options");break;case"recordingState":console.log("[WebView] [DEBUG] Received recordingState message:",m),console.log("[WebView] [DEBUG] Current recording state before update:",{isRecording:r,isPaused:s,elapsedTime:c}),void 0!==m.isRecording&&(console.log(`[WebView] [DEBUG] Updating isRecording from ${r} to ${m.isRecording}`),i(m.isRecording)),void 0!==m.isPaused&&(console.log(`[WebView] [DEBUG] Updating isPaused from ${s} to ${m.isPaused}`),l(m.isPaused)),void 0!==m.elapsedTime&&(console.log(`[WebView] [DEBUG] Updating elapsedTime from ${c} to ${m.elapsedTime}`),d(m.elapsedTime));break;case"updateTranscriptions":if(m.transcriptions){if(console.log(`WebView: Received ${m.transcriptions.length} transcriptions from extension`),console.log("WebView: Current transcriptions count before update:",_.length),0===m.transcriptions.length)console.log("WebView: Received empty transcriptions array - this should clear the history");else if(m.transcriptions.length>0){var p;const e=m.transcriptions[0];console.log("WebView: Most recent transcription:",{id:e.id,timestamp:e.timestamp,originalTextLength:(null===(p=e.originalText)||void 0===p?void 0:p.length)||0,hasOptimized:!!e.optimizedText,service:e.service})}J(m.transcriptions),console.log("WebView: Transcription state updated to",m.transcriptions.length,"items")}else console.warn("WebView: Received updateTranscriptions message with no transcriptions data");break;case"optimizationComplete":m.id&&m.optimizedText&&J(e=>e.map(e=>e.id===m.id?{...e,optimizedText:m.optimizedText}:e));break;case"transcriptionDeleted":m.id&&J(e=>e.filter(e=>e.id!==m.id));break;case"allTranscriptionsCleared":console.log("WebView: Received allTranscriptionsCleared message"),console.log("WebView: Current transcriptions count before clearing:",_.length),J([]),console.log("WebView: Transcriptions cleared");break;case"error":console.error("Error from extension:",m.message);break;case"updateAudioDevices":m.devices&&F(m.devices);break;case"updateAudioSettings":if(m.settings){const{sampleRate:e,device:t}=m.settings;16e3===e&&!0===L?(console.log("[WebView] Updating sample rate for real-time mode:",e),I(e)):16e3!==e&&!1===L&&le.current&&(console.log("[WebView] Restoring previous sample rate after real-time mode:",le.current),I(le.current)),void 0===t||a||(console.log("[WebView] Initial setting of device ID from backend:",t),G(t))}}};return window.addEventListener("message",o),T.postMessage({command:"getOptions"}),T.postMessage({command:"getTranscriptions"}),T.postMessage({command:"getAudioSettings"}),()=>{window.removeEventListener("message",o)}},[Y,a,u,b,x,S,O,P,L,N,N.length,W,B,K,_.length,r,s,c]);const Q=Object(o.useCallback)(()=>{console.log("[WebView] [DEBUG] Start recording called, current state:",{isRecording:r,isPaused:s}),T.postMessage({command:"startRecording",options:{service:u,model:b,language:x,optimize:S,optimizationModel:S?O:"",customPrompt:S?N:"",translate:P,realtime:L}}),console.log("[WebView] [DEBUG] Optimistically updating UI state to recording"),i(!0),l(!1)},[u,b,x,S,O,N,P,L,r,s]),X=Object(o.useCallback)(()=>{console.log("[WebView] [DEBUG] Stop recording called, current state:",{isRecording:r,isPaused:s}),T.postMessage({command:"stopRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to stopped"),i(!1),l(!1)},[r,s]),Z=Object(o.useCallback)(()=>{console.log("[WebView] [DEBUG] Pause recording called, current state:",{isRecording:r,isPaused:s}),T.postMessage({command:"pauseRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to paused"),l(!0)},[r,s]),ee=Object(o.useCallback)(()=>{console.log("[WebView] [DEBUG] Resume recording called, current state:",{isRecording:r,isPaused:s}),T.postMessage({command:"resumeRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to resumed"),l(!1)},[r,s]),te=Object(o.useCallback)(()=>{console.log("[WebView] [DEBUG] Cancel recording called, current state:",{isRecording:r,isPaused:s}),T.postMessage({command:"cancelRecording"}),console.log("[WebView] [DEBUG] Optimistically updating UI state to cancelled"),i(!1),l(!1)},[r,s]),ae=Object(o.useCallback)(t=>{e.current.service=Date.now(),console.log(`WebView: Service change requested from ${u} to ${t}`),console.log(`WebView: Current model before change: ${b}`),console.log(`WebView: Current language before change: ${x}`);let a=b,o=x,n=!1;if("openai"===t)"assemblyai"===u&&(console.log(`WebView: Saving AssemblyAI model ${b} for future use`),y(b)),a="whisper-1",console.log(`WebView: Setting model to ${a} for OpenAI service`),e.current.model=Date.now();else if("assemblyai"===t){a=v,console.log(`WebView: Restoring AssemblyAI model to ${a}`),e.current.model=Date.now();const t=["en","es","fr","de","it","pt","nl","hi","ja","zh","ar"];"auto"!==x&&t.includes(x)||(console.log(`WebView: Language ${x} not supported by AssemblyAI, changing to en`),o="en",n=!0,e.current.language=Date.now()),P&&(console.log("WebView: Disabling translate for AssemblyAI service"),e.current.translate=Date.now(),U(!1))}g(t),k(a),n&&E(o),T.postMessage({command:"updateOptions",options:{service:t,model:a,...n?{language:o}:{},..."assemblyai"===t&&P?{translate:!1}:{}}}),console.log(`WebView: Service changed to ${t}, model set to ${a}, language: ${n?o:"unchanged"}`)},[u,b,x,v,P]),oe=Object(o.useCallback)(t=>{e.current.model=Date.now(),k(t),T.postMessage({command:"updateOptions",options:{model:t}})},[]),ne=Object(o.useCallback)(t=>{e.current.language=Date.now(),E(t),T.postMessage({command:"updateOptions",options:{language:t}})},[]),re=Object(o.useCallback)(t=>{e.current.customPrompt=Date.now(),C(t),window.promptUpdateTimeout&&clearTimeout(window.promptUpdateTimeout),window.promptUpdateTimeout=setTimeout(()=>{T.postMessage({command:"updateOptions",options:{customPrompt:t}}),console.log(`Custom prompt updated (debounced): ${t.substring(0,30)}${t.length>30?"...":""}`)},500)},[]);Object(o.useEffect)(()=>()=>{window.promptUpdateTimeout&&clearTimeout(window.promptUpdateTimeout)},[]);const ie=Object(o.useCallback)(t=>{e.current.optimize=Date.now(),M(t),T.postMessage({command:"updateOptions",options:{optimize:t}})},[]),se=Object(o.useCallback)(t=>{a&&(U(t),e.current.translate=Date.now(),T.postMessage({command:"updateOptions",options:{translate:t}}))},[a]),le=Object(o.useRef)(0),ce=Object(o.useCallback)(e=>{a&&(console.log("[WebView] Toggling real-time transcription:",e),V(e),e?(le.current=W,console.log("[WebView] Storing current sample rate before real-time:",le.current),I(16e3)):le.current&&(console.log("[WebView] Restoring sample rate after real-time:",le.current),I(le.current)),T.postMessage({command:"updateOptions",options:{realtime:e}}))},[a,W]),de=Object(o.useCallback)(t=>{e.current.optimizationModel=Date.now(),j(t),T.postMessage({command:"updateOptions",options:{optimizationModel:t}}),console.log(`WebView: Optimization model changed to ${t}`)},[]),me=Object(o.useCallback)(e=>{const t=_.find(t=>t.id===e);t&&T.postMessage({command:"copyToClipboard",text:t.originalText})},[_]),ue=Object(o.useCallback)(e=>{const t=_.find(t=>t.id===e);t&&t.optimizedText&&T.postMessage({command:"copyToClipboard",text:t.optimizedText})},[_]),pe=Object(o.useCallback)(e=>{const t=_.find(t=>t.id===e);t&&(T.postMessage({command:"optimizeTranscription",id:e,text:t.originalText,customPrompt:N,optimizationModel:O}),J(t=>t.map(t=>t.id===e?{...t,optimizedText:"Optimizing..."}:t)))},[_,N,O]),ge=Object(o.useCallback)(e=>{console.log("[WebView] Changing sample rate to:",e),I(e),T.postMessage({command:"updateSetting",key:"voicehype.audio.sampleRate",value:e})},[]),be=Object(o.useCallback)(t=>{G(t),e.current.deviceId=Date.now(),window.deviceUpdateTimeout&&clearTimeout(window.deviceUpdateTimeout),window.deviceUpdateTimeout=setTimeout(()=>{const e=null===t?"":t;T.postMessage({command:"updateAudioDevice",deviceId:e}),console.log(`[WebView] Sending audio device change to backend (debounced): ${e}`)},500)},[]);return Object(o.useEffect)(()=>()=>{window.deviceUpdateTimeout&&clearTimeout(window.deviceUpdateTimeout)},[]),o.createElement("div",{className:"sm:px-0 flex flex-col h-full px-1"},o.createElement(R,{className:"py-4 mb-2"}),o.createElement("div",{className:"mb-5"},o.createElement(m,{isRecording:r,isPaused:s,elapsedTime:c,onStart:Q,onStop:X,onPause:Z,onResume:ee,onCancel:te})),o.createElement("div",{className:"mb-4 space-y-4"},o.createElement(p,{service:u,onChange:ae}),o.createElement(h,{service:u,model:b,onChange:oe}),o.createElement(f,{service:u,model:b,language:x,onChange:ne})),o.createElement("div",{className:"mb-4"},o.createElement(z,{sampleRate:W,deviceId:B,availableDevices:H,onSampleRateChange:ge,onDeviceChange:be})),o.createElement("div",{className:"mb-4 space-y-2"},"openai"===u&&o.createElement(D,{checked:P,onChange:se,label:"Translate to English"}),"assemblyai"===u&&"best"===b&&o.createElement(D,{checked:L,onChange:ce,label:"Use real-time transcription"}),o.createElement(D,{checked:S,onChange:ie,label:"Optimize transcription"})),S&&o.createElement("div",{className:"mb-4 space-y-4"},o.createElement("div",null,o.createElement("h2",{className:"text-md mb-2 font-medium"},"Optimization Mode"),o.createElement(A,{value:N,onChange:re,onModeChange:e=>{T.postMessage({command:"setActivePromptMode",modeId:e})}})),o.createElement("div",null,o.createElement("h2",{className:"text-md mb-2 font-medium"},"Optimization Model"),o.createElement(w,{optimizationModel:O,onChange:de}))),o.createElement("div",{className:"flex-grow overflow-hidden"},o.createElement("h2",{className:"text-md mb-2 font-medium"},"Transcription History"),o.createElement("div",{className:"h-[calc(100%-1.75rem)]"},o.createElement($,{transcriptions:_,onCopyOriginal:me,onCopyOptimized:ue,onOptimize:pe}))))};const W=(()=>{var e;const t=document.body.classList.contains("vscode-dark"),a=document.body.classList.contains("vs-dark"),o=null===(e=document.querySelector("html"))||void 0===e?void 0:e.getAttribute("data-vscode-theme-kind"),n="vscode-dark"===o||"dark"===o,r=window.getComputedStyle(document.body).backgroundColor.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);let i=!1;r&&4===r.length&&(i=(299*parseInt(r[1])+587*parseInt(r[2])+114*parseInt(r[3]))/1e3<128);return t||a||n||i})();document.documentElement.classList.add(W?"vscode-dark":"vscode-light"),document.body.classList.add(W?"vscode-dark":"vscode-light"),W&&document.documentElement.classList.add("dark"),console.log("VS Code theme detected:",W?"dark":"light"),r.render(o.createElement(o.StrictMode,null,o.createElement(V,null)),document.getElementById("root"))},4:function(e,t,a){e.exports=a(14)}},[[4,1,2]]]);