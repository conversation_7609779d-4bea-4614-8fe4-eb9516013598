<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import ApiKeyInput from './components/ApiKeyInput.vue'
import DarkModeToggle from './components/DarkModeToggle.vue'
import CustomPrompt from './components/CustomPrompt.vue'
import Logo from './components/Logo.vue'
import ModelSelector from './components/ModelSelector.vue'
import OptimizationModelSelector from './components/OptimizationModelSelector.vue'
import RecordingControls from './components/RecordingControls.vue'
import ServiceSelector from './components/ServiceSelector.vue'
import LanguageSelector from './components/LanguageSelector.vue'
import AudioSettings from './components/AudioSettings.vue'
import Switch from './components/Switch.vue'
import RecentTranscriptions from './components/RecentTranscriptions.vue'
import PromptModeSelector from './components/PromptModeSelector.vue'
import { vscode } from './utilities/vscode'

interface Transcription {
  id: string
  timestamp: string
  originalText: string
  optimizedText?: string
  service: string
  model: string
  language: string
}

interface LastUpdates {
  service: number
  model: number
  language: number
  optimize: number
  optimizationModel: number
  translate: number
  customPrompt: number
  realtime: number
  sampleRate: number
  deviceId: number
}

interface AudioDevice {
  id: string
  name: string
}

interface StateRef {
  service: string
  model: string
  language: string
  optimizeEnabled: boolean
  optimizationModel: string
  translate: boolean
  realtime: boolean
  customPrompt: string
  sampleRate: number
  deviceId: string | null
  apiKey: string
}

// State management
const initialConfigLoaded = ref(false)
const isRecording = ref(false)
const isPaused = ref(false)
const elapsedTime = ref(0)
const service = ref('assemblyai')
const model = ref('whisper-1')
const lastAssemblyAIModel = ref('best')
const language = ref('en')
const customPrompt = ref('Correct any grammar issues and improve clarity. Keep the meaning intact.')
const handlePromptModeChange = (modeId: string) => {
  // Handle mode change if needed
}
const optimizeEnabled = ref(true)
const optimizationModel = ref('gpt-4o')
const translate = ref(false)
const realtime = ref(false)
const sampleRate = ref(22050)
const deviceId = ref<string | null>(null)
const availableDevices = ref<AudioDevice[]>([])
const apiKey = ref('')
const transcriptions = ref<Transcription[]>([])

const lastUpdates = ref<LastUpdates>({
  service: 0,
  model: 0,
  language: 0,
  optimize: 0,
  optimizationModel: 0,
  translate: 0,
  customPrompt: 0,
  realtime: 0,
  sampleRate: 0,
  deviceId: 0
})

const stateRef = ref<StateRef>({
  service: service.value,
  model: model.value,
  language: language.value,
  optimizeEnabled: optimizeEnabled.value,
  optimizationModel: optimizationModel.value,
  translate: translate.value,
  realtime: realtime.value,
  customPrompt: customPrompt.value,
  sampleRate: sampleRate.value,
  deviceId: deviceId.value,
  apiKey: apiKey.value
})

// Keep stateRef in sync
watch([service, model, language, optimizeEnabled, optimizationModel, translate, realtime, customPrompt, sampleRate, deviceId, apiKey], () => {
  stateRef.value = {
    service: service.value,
    model: model.value,
    language: language.value,
    optimizeEnabled: optimizeEnabled.value,
    optimizationModel: optimizationModel.value,
    translate: translate.value,
    realtime: realtime.value,
    customPrompt: customPrompt.value,
    sampleRate: sampleRate.value,
    deviceId: deviceId.value,
    apiKey: apiKey.value
  }
})

// Message handling
const handleMessage = (event: MessageEvent) => {
  const message = event.data
  // Implement message handling similar to React version
  // ...
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
  vscode.postMessage({ command: 'getOptions' })
  vscode.postMessage({ command: 'getTranscriptions' })
  vscode.postMessage({ command: 'getAudioSettings' })

  return () => {
    window.removeEventListener('message', handleMessage)
  }
})

// Recording handlers
const handleStartRecording = () => {
  isRecording.value = true
  isPaused.value = false
  vscode.postMessage({ command: 'startRecording' })
}

const handleStopRecording = () => {
  isRecording.value = false
  isPaused.value = false
  vscode.postMessage({ command: 'stopRecording' })
}

const handlePauseRecording = () => {
  isPaused.value = true
  vscode.postMessage({ command: 'pauseRecording' })
}

const handleResumeRecording = () => {
  isPaused.value = false
  vscode.postMessage({ command: 'resumeRecording' })
}

const handleCancelRecording = () => {
  isRecording.value = false
  isPaused.value = false
  vscode.postMessage({ command: 'cancelRecording' })
}

// Service and model handlers
const handleServiceChange = (newService: string) => {
  service.value = newService
  vscode.postMessage({ command: 'updateService', service: newService })
}

const handleModelChange = (newModel: string) => {
  model.value = newModel
  vscode.postMessage({ command: 'updateModel', model: newModel })
}

const handleLanguageChange = (newLanguage: string) => {
  language.value = newLanguage
  vscode.postMessage({ command: 'updateLanguage', language: newLanguage })
}

// Audio settings handlers
const handleSampleRateChange = (newSampleRate: number) => {
  sampleRate.value = newSampleRate
  vscode.postMessage({ command: 'updateSampleRate', sampleRate: newSampleRate })
}

const handleDeviceChange = (newDeviceId: string | null) => {
  deviceId.value = newDeviceId
  vscode.postMessage({ command: 'updateDevice', deviceId: newDeviceId })
}

// Toggle handlers
const handleTranslateToggle = (checked: boolean) => {
  translate.value = checked
  vscode.postMessage({ command: 'updateTranslate', translate: checked })
}

const handleRealtimeToggle = (checked: boolean) => {
  realtime.value = checked
  vscode.postMessage({ command: 'updateRealtime', realtime: checked })
}

const handleOptimizeToggle = (checked: boolean) => {
  optimizeEnabled.value = checked
  vscode.postMessage({ command: 'updateOptimize', optimize: checked })
}

// Transcription handlers
const handleCopyOriginal = (text: string) => {
  vscode.postMessage({ command: 'copyText', text })
}

const handleCopyOptimized = (text: string) => {
  vscode.postMessage({ command: 'copyText', text })
}

const handleOptimizationModelChange = (newModel: string) => {
  optimizationModel.value = newModel
  vscode.postMessage({ command: 'updateOptimizationModel', model: newModel })
}

const handleOptimize = (transcriptionId: string) => {
  vscode.postMessage({ command: 'optimizeTranscription', id: transcriptionId })
}
</script>

<template>
  <div class="min-w-[320px] flex flex-col h-full px-2 sm:px-4 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
    <!-- Header Section -->
    <div class="flex items-center justify-between py-4 mb-2">
      <Logo />
      <DarkModeToggle />
    </div>

    <!-- API Key Input -->
    <ApiKeyInput :initialApiKey="apiKey" />

    <!-- Recording controls -->
    <div class="mb-5">
      <RecordingControls
        :isRecording="isRecording"
        :isPaused="isPaused"
        :elapsedTime="elapsedTime"
        @start="handleStartRecording"
        @stop="handleStopRecording"
        @pause="handlePauseRecording"
        @resume="handleResumeRecording"
        @cancel="handleCancelRecording"
      />
    </div>

    <!-- Service and model settings -->
    <div class="min-w-0 mb-4 space-y-4">
      <ServiceSelector
        :service="service"
        @change="handleServiceChange"
      />

      <ModelSelector
        :service="service"
        :model="model"
        @change="handleModelChange"
      />

      <LanguageSelector
        :service="service"
        :model="model"
        :language="language"
        @change="handleLanguageChange"
      />

      <div class="space-y-2">
        <Switch
          v-if="service === 'assemblyai' && model === 'best'"
          :checked="realtime"
          @change="handleRealtimeToggle"
          label="Use real-time transcription"
        />
      </div>
    </div>

    <!-- Audio Settings -->
    <div class="mb-4">
      <AudioSettings
        :sampleRate="sampleRate"
        :deviceId="deviceId"
        :availableDevices="availableDevices"
        @sampleRateChange="handleSampleRateChange"
        @deviceChange="handleDeviceChange"
      />
    </div>

    <!-- Optimization and translation toggles -->
    <div class="mb-4 space-y-2">
      <Switch
        :checked="optimizeEnabled"
        @change="handleOptimizeToggle"
        label="Optimize transcription"
      />
      <Switch
        v-if="service === 'openai'"
        :checked="translate"
        @change="handleTranslateToggle"
        label="Translate to English"
      />
    </div>

    <!-- Custom Prompt -->
    <div class="mb-4">
      <CustomPrompt v-model="customPrompt" />
    </div>

    <!-- Prompt Mode Selector -->
    <div class="mb-4">
      <PromptModeSelector
        v-model="customPrompt"
        @modeChange="handlePromptModeChange"
      />
    </div>

    <!-- Optimization Model Selector -->
    <div class="mb-4">
      <OptimizationModelSelector
        :optimizationModel="optimizationModel"
        @change="handleOptimizationModelChange"
      />
    </div>


    <!-- Recent transcriptions -->
    <div class="h-[calc(100%-1.75rem)] overflow-auto">
      <RecentTranscriptions
        :transcriptions="transcriptions"
        @copyOriginal="handleCopyOriginal"
        @copyOptimized="handleCopyOptimized"
        @optimize="handleOptimize"
      />
    </div>
  </div>
</template>
