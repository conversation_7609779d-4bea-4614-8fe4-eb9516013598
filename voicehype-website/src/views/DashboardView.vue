<template>
  <!-- Dashboard Section -->
  <section id="dashboard" class="mb-12">
    <h2 class="text-gray-800 dark:text-[#c9d1d9] font-heading mb-6 text-2xl font-semibold">Dashboard</h2>

    <!-- Loading Shimmer for Cards -->
    <div v-if="loading" class="md:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-6">
      <div v-for="i in 4" :key="i" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129] shimmer-card animate-pulse">
        <div class="flex items-center">
          <div class="bg-gray-200 dark:bg-[#1c2129] p-3 rounded-full shimmer-element"></div>
          <div class="w-full ml-4 space-y-2">
            <div class="bg-gray-200 dark:bg-[#1c2129] w-24 h-3 rounded shimmer-element"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] w-16 h-5 rounded shimmer-element"></div>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-[#1c2129] rounded-full h-2.5 shimmer-element"></div>
        </div>
      </div>
    </div>

    <!-- Actual Cards -->
    <div v-else class="md:grid-cols-2 lg:grid-cols-3 grid grid-cols-1 gap-6">
      <!-- Transcription Usage Card (shown if user has quotas) -->
      <div v-if="hasQuotasToShow && transcriptionUsage.total > 0" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300 p-3 rounded-full">
            <MicrophoneIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Transcription Usage</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              {{ formatAmount(transcriptionUsage.used, 'transcription') }} / {{ formatAmount(transcriptionUsage.total, 'transcription') }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-[#1c2129] rounded-full h-2.5">
            <div
              class="bg-primary-600 dark:bg-[#14F195] h-2.5 rounded-full"
              :style="{ width: `${Math.max(transcriptionUsage.percentage, 1)}%` }"
            ></div>
          </div>
          <div v-if="hasExpiredFreeTrialPlan" class="mt-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
              Trial Expired
            </span>
          </div>
        </div>
      </div>

      <!-- Optimization/Tokens Usage Card (shown if user has quotas) -->
      <div v-if="hasQuotasToShow && optimizationUsage.total > 0" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-3 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Token Usage</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              {{ formatAmount(optimizationUsage.used, 'optimization') }} / {{ formatAmount(optimizationUsage.total, 'optimization') }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="w-full bg-gray-200 dark:bg-[#1c2129] rounded-full h-2.5">
            <div
              class="bg-blue-600 dark:bg-blue-400 h-2.5 rounded-full"
              :style="{ width: `${Math.max(optimizationUsage.percentage, 1)}%` }"
            ></div>
          </div>
          <div v-if="hasExpiredFreeTrialPlan" class="mt-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
              Trial Expired
            </span>
          </div>
        </div>
      </div>

      <!-- Credits Card -->
      <div class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300 p-3 rounded-full">
            <CurrencyDollarIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Credits Balance</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              ${{ creditBalance }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <router-link
            to="/app/payments"
            class="text-secondary-600 dark:text-secondary-400 hover:text-secondary-800 dark:hover:text-secondary-300 text-sm"
          >
            <PlusCircleIcon class="inline w-5 h-5 mr-1" /> Add Credits
          </router-link>
        </div>
      </div>

      <!-- API Keys Card -->
      <div class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="dark:bg-green-900 dark:text-green-400 p-3 text-green-600 bg-green-100 rounded-full">
            <KeyIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Active API Keys</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              {{ activeApiKeysCount }}
            </p>
          </div>
        </div>
        <div class="mt-4">
          <router-link
            to="/app/api-keys"
            class="dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 text-sm text-green-600"
          >
            <PlusCircleIcon class="inline w-5 h-5 mr-1" /> Create New Key
          </router-link>
        </div>
      </div>

      <!-- Free Trial Card (active) -->
      <div v-if="hasActiveFreeTrialPlan" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="dark:bg-purple-900 dark:text-purple-400 p-3 text-purple-600 bg-purple-100 rounded-full">
            <CreditCardIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Current Plan</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              Free Trial
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="text-gray-600 dark:text-[#8b949e] text-sm">
            Expires: {{ userSubscription?.current_period_end ? formatDate(userSubscription.current_period_end) : 'N/A' }}
          </div>
        </div>
      </div>

      <!-- Expired Free Trial Card -->
      <div v-else-if="hasExpiredFreeTrialPlan" class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-red-200 dark:border-red-800">
        <div class="flex items-center">
          <div class="dark:bg-red-900 dark:text-red-400 p-3 text-red-600 bg-red-100 rounded-full">
            <CreditCardIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Current Plan</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              Free Trial (Expired)
            </p>
          </div>
        </div>
        <div class="mt-4">
          <div class="text-red-600 dark:text-red-400 text-sm mb-2">
            Expired: {{ userSubscription?.current_period_end ? formatDate(userSubscription.current_period_end) : 'N/A' }}
          </div>
          <router-link
            to="/app/payments"
            class="dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm text-purple-600"
          >
            <ArrowUpCircleIcon class="inline w-5 h-5 mr-1" /> Upgrade Plan
          </router-link>
        </div>
      </div>

      <!-- PAYG Card (shown if no subscription) -->
      <div v-else class="bg-white dark:bg-[#0d1117] p-6 rounded-lg shadow-sm border border-gray-200 dark:border-[#1c2129]">
        <div class="flex items-center">
          <div class="dark:bg-purple-900 dark:text-purple-400 p-3 text-purple-600 bg-purple-100 rounded-full">
            <CreditCardIcon class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <p class="text-gray-500 dark:text-[#8b949e] text-sm">Payment Method</p>
            <p class="text-gray-700 dark:text-[#c9d1d9] text-lg font-semibold">
              Not Set Up
            </p>
          </div>
        </div>
        <div class="mt-4">
          <router-link
            to="/app/payments"
            class="dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 text-sm text-purple-600"
          >
            <ArrowUpCircleIcon class="inline w-5 h-5 mr-1" /> Add Payment Method
          </router-link>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8">
      <h3 class="text-gray-800 dark:text-[#c9d1d9] font-heading mb-4 text-lg font-semibold">Recent Activity</h3>

      <!-- Loading Shimmer for Recent Activity -->
      <div v-if="loading" class="bg-white dark:bg-[#0d1117] overflow-hidden rounded-lg shadow border border-gray-200 dark:border-[#1c2129]">
        <div class="animate-pulse">
          <!-- Table Header Shimmer -->
          <div class="bg-gray-50 dark:bg-[#0d1117] flex px-6 py-3 space-x-4">
            <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-3 rounded"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-3 rounded"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-3 rounded"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-3 rounded"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-3 rounded"></div>
            <div class="bg-gray-200 dark:bg-[#1c2129] md:block hidden w-1/6 h-3 rounded"></div>
          </div>

          <!-- Table Rows Shimmer -->
          <div v-for="i in 5" :key="i" class="border-t border-gray-200 dark:border-[#1c2129]">
            <div class="flex px-6 py-4 space-x-4">
              <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-4 rounded"></div>
              <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-4 rounded"></div>
              <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-4 rounded"></div>
              <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-4 rounded"></div>
              <div class="bg-gray-200 dark:bg-[#1c2129] w-1/6 h-4 rounded"></div>
              <div class="bg-gray-200 dark:bg-[#1c2129] md:block hidden w-1/6 h-4 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actual Recent Activity Table -->
      <div v-else class="bg-white dark:bg-[#0d1117] rounded-lg shadow">
        <div class="overflow-x-auto">
          <table class="min-w-full">
            <thead class="bg-gray-50 dark:bg-[#0d1117]">
              <tr>
                <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Service</th>
                <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase max-w-[150px]">Model</th>
                <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Amount</th>
                <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Cost</th>
                <th class="text-gray-500 dark:text-[#8b949e] px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Date</th>
                <th class="text-gray-500 dark:text-[#8b949e] md:table-cell hidden px-6 py-3 text-xs font-medium tracking-wider text-left uppercase">Details</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-[#0d1117]">
              <tr v-for="(activity, index) in recentActivity" :key="activity.id"
                  :class="index % 2 === 0 ? 'bg-white dark:bg-[#0d1117]' : 'bg-gray-50 dark:bg-[#10161d]'">
                <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                  {{ activity.service }}
                </td>
                <td class="text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm max-w-[150px] break-words">
                  {{ activity.model }}
                </td>
                <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                  {{ formatAmount(activity.amount, activity.service) }}
                </td>
                <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                  ${{ formatCost(activity.cost) }}
                </td>
                <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] px-6 py-4 text-sm">
                  {{ formatDate(activity.created_at) }}
                </td>
                <td class="whitespace-nowrap text-gray-500 dark:text-[#8b949e] md:table-cell hidden px-6 py-4 text-sm">
                  <div v-if="activity.metadata && (activity.metadata.inputTokens || activity.metadata.outputTokens)" class="flex flex-row space-x-2">
                    <span v-if="activity.metadata.inputTokens" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      Input: {{ activity.metadata.inputTokens }} tokens
                    </span>
                    <span v-if="activity.metadata.outputTokens" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      Output: {{ activity.metadata.outputTokens }} tokens
                    </span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="mt-4 text-right">
        <router-link to="/app/usage" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
          View All Activity →
        </router-link>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { useCreditsStore } from '@/stores/credits'
import { useApiKeysStore } from '@/stores/apiKeys'
import { useUsageStore } from '@/stores/usage'
import {
  MicrophoneIcon,
  CurrencyDollarIcon,
  KeyIcon,
  CreditCardIcon,
  PlusCircleIcon,
  ArrowUpCircleIcon
} from '@heroicons/vue/24/outline'

// Stores
const subscriptionStore = useSubscriptionStore()
const creditsStore = useCreditsStore()
const apiKeysStore = useApiKeysStore()
const usageStore = useUsageStore()

// State
const loading = ref(true)

// Computed
const transcriptionUsage = computed(() => {
  // Show quota usage if user has any subscription (active or expired) or complimentary quotas
  if (subscriptionStore.hasSubscription || subscriptionStore.quotas.length > 0) {
    return subscriptionStore.getQuotaUsage('transcription')
  }
  return { used: 0, total: 0, percentage: 0 }
})

const optimizationUsage = computed(() => {
  // Show quota usage for optimization/tokens
  if (subscriptionStore.hasSubscription || subscriptionStore.quotas.length > 0) {
    return subscriptionStore.getQuotaUsage('optimization')
  }
  return { used: 0, total: 0, percentage: 0 }
})

const creditBalance = computed(() => {
  return creditsStore.credits?.balance.toFixed(2) || '0.00'
})

const activeApiKeysCount = computed(() => {
  return apiKeysStore.apiKeys.filter(key => key.is_active).length
})

const userSubscription = computed(() => {
  return subscriptionStore.userSubscription
})

const hasActiveFreeTrialPlan = computed(() => {
  return (
    subscriptionStore.hasActiveSubscription &&
    subscriptionStore.currentPlan?.name === 'Free Trial'
  )
})

const hasExpiredFreeTrialPlan = computed(() => {
  return (
    subscriptionStore.hasSubscription &&
    subscriptionStore.isSubscriptionExpired &&
    subscriptionStore.currentPlan?.name === 'Free Trial'
  )
})

const hasQuotasToShow = computed(() => {
  return subscriptionStore.quotas.length > 0
})

const recentActivity = computed(() => {
  return usageStore.usageHistory.slice(0, 5)
})

// Methods
function formatAmount(amount: number, service: string) {
  if (service === 'transcription') {
    if (amount < 1) {
      // Convert to seconds and display with up to 3 decimal places
      const seconds = amount * 60;
      // Remove trailing zeros
      return `${parseFloat(seconds.toFixed(3))} secs`;
    }
    // For whole numbers, don't show decimal places
    if (Math.floor(amount) === amount) {
      return `${Math.floor(amount)} mins`;
    }
    // For numbers with decimals, show up to 1 decimal place
    return `${parseFloat(amount.toFixed(1))} mins`;
  } else if (service === 'optimization') {
    return `${Math.round(amount)} tokens`;
  }
  return amount.toString();
}

function formatCost(cost: number) {
  if (cost === 0) return '0';

  // Format with up to 7 decimal places, but remove trailing zeros
  const formatted = cost.toFixed(7);
  return parseFloat(formatted).toString();
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
  } else if (date.toDateString() === yesterday.toDateString()) {
    return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) +
           `, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
  }
}

// Lifecycle hooks
onMounted(async () => {
  loading.value = true

  try {
    // Fetch subscription and other data first
    await Promise.all([
      subscriptionStore.fetchUserSubscription(),
      creditsStore.fetchCredits(),
      apiKeysStore.fetchApiKeys(),
      usageStore.fetchUsageHistory()
    ])

    // Always fetch quotas after subscription data is loaded
    // This will fetch both subscription quotas (if active) and complimentary quotas
    await subscriptionStore.fetchQuotas()
  } catch (err) {
    console.error('Error loading dashboard data:', err)
  } finally {
    loading.value = false
  }
})
</script>