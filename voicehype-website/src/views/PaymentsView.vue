<template>
  <Transition name="fade" appear>
  <div class="max-w-3xl mx-auto">
      <h1 class="dark:text-white mb-8 text-2xl font-bold text-gray-900">Payments</h1>

    <!-- Credit Balance Section -->
    <div class="card mb-8">
        <h2 class="dark:text-white mb-4 text-xl font-semibold text-gray-900">Credit Balance</h2>

      <div v-if="creditsStore.loading" class="py-4">
        <div class="animate-pulse flex space-x-4">
          <div class="flex-1 py-1 space-y-4">
              <div class="w-3/4 h-4 bg-gray-200 dark:bg-[#21262d] rounded"></div>
              <div class="w-1/2 h-4 bg-gray-200 dark:bg-[#21262d] rounded"></div>
            </div>
        </div>
      </div>

      <div v-else-if="creditsStore.error" class="py-4 text-red-500">
        Failed to load credit balance: {{ creditsStore.error }}
      </div>

      <div v-else>
        <div class="flex items-baseline mb-4">
            <span class="dark:text-white mr-2 text-3xl font-bold text-gray-900">${{ creditBalance }}</span>
            <span class="text-gray-600 dark:text-[#8b949e]">available credits</span>
        </div>

        <!-- Credit Breakdown Table -->
        <div v-if="creditsStore.activePurchases.length > 0" class="mb-4">
            <h3 class="mb-2 text-sm font-medium text-gray-500 dark:text-[#8b949e]">Credit Breakdown</h3>
          <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-[#30363d]">
              <thead>
                <tr>
                    <th
                      class="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-[#8b949e]">
                      Amount</th>
                    <th
                      class="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-[#8b949e]">
                      Purchase Date</th>
                    <th
                      class="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-[#8b949e]">
                      Expires</th>
                </tr>
              </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-[#30363d]">
                <tr v-for="purchase in creditsStore.activePurchases" :key="purchase.id">
                    <td class="whitespace-nowrap dark:text-white px-4 py-2 text-sm font-medium text-gray-900">${{
                      purchase.remaining.toFixed(2) }}</td>
                    <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500 dark:text-[#8b949e]">{{
                      formatDate(purchase.created_at) }}</td>
                    <td class="whitespace-nowrap px-4 py-2 text-sm text-gray-500 dark:text-[#8b949e]">{{
                      formatDate(purchase.expires_at) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

          <div v-else class="mb-4 text-gray-500 dark:text-[#8b949e]">
          You don't have any active credits. Purchase credits below to get started.
        </div>

        <button class="btn-primary" @click="showBuyCreditsModal = true" :disabled="creditsStore.checkoutLoading">
          <span v-if="creditsStore.checkoutLoading">Creating checkout...</span>
          <span v-else>Buy More Credits</span>
        </button>
      </div>
    </div>

    <!-- Free Trial Section (only shown if free trial is active) -->
    <div v-if="hasActiveFreeTrialPlan" class="card mb-8">
        <h2 class="dark:text-white mb-4 text-xl font-semibold text-gray-900">Free Trial</h2>
        <p class="mb-4 text-gray-700 dark:text-[#c9d1d9]">
        You are currently on the Free Trial plan. This gives you limited access to VoiceHype features.
      </p>
        <p class="mb-4 text-gray-700 dark:text-[#c9d1d9]">
          Your trial expires on <strong class="dark:text-white text-gray-900">{{
            formatDate(userSubscription?.current_period_end) }}</strong>.
      </p>
        <p class="mb-4 text-sm text-gray-500 dark:text-[#8b949e]">
        After your trial ends, you'll need to purchase credits to continue using VoiceHype.
      </p>
    </div>

    <!-- Pay As You Go Section -->
    <div class="card mb-8">
        <h2 class="dark:text-white mb-4 text-xl font-semibold text-gray-900">Pay as you go</h2>

      <div class="md:flex-row md:items-start md:space-x-4 flex flex-col">
        <div class="md:mb-0 flex-1 mb-6">
          <div class="flex items-center mb-1">
              <h3 class="text-lg font-medium text-gray-800 dark:text-[#c9d1d9]">Credit balance</h3>
              <button class="ml-2 text-gray-500 hover:text-gray-700 dark:text-[#8b949e] dark:hover:text-[#c9d1d9]">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </button>
          </div>

            <div class="dark:text-white mb-2 text-3xl font-bold text-gray-900">${{ creditBalance }}</div>

            <button class="hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-blue-600"
              @click="showBuyCreditsModal = true">
            Add to credit balance
          </button>
        </div>

        <div class="flex-1">
          <div class="p-4 bg-gray-50 dark:bg-[#21262d] rounded-lg">
            <div class="flex items-center justify-between mb-4">
                <span class="text-sm text-gray-500 dark:text-[#8b949e]">Auto recharge is {{ autoRecharge ? 'on' : 'off'
                  }}</span>
              <label class="inline-flex items-center cursor-pointer">
                <input type="checkbox" v-model="autoRecharge" class="peer sr-only">
                  <div
                    class="relative w-11 h-6 bg-gray-200 dark:bg-[#30363d] peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-[#30363d] peer-checked:bg-blue-600">
                  </div>
              </label>
            </div>

              <p class="text-sm text-gray-600 dark:text-[#c9d1d9]">
                When your credit balance reaches $0, your API requests will stop working. Enable automatic recharge to
                automatically keep your credit balance topped up.
            </p>

              <button v-if="!autoRecharge"
                class="hover:bg-green-700 px-4 py-2 mt-4 text-sm font-medium text-white transition-colors bg-green-600 rounded">
              Enable auto recharge
            </button>
          </div>
        </div>
      </div>
    </div>

      <!-- Buy Credits Modal -->
      <Transition name="modal">
        <div v-if="showBuyCreditsModal"
          class="backdrop-blur-sm fixed inset-0 z-50 flex items-center justify-center overflow-hidden transition-all duration-300">
          <div
            class="w-full max-w-md max-h-[70vh] mx-4 bg-white dark:bg-[#161b22] rounded-lg shadow-xl flex flex-col transform transition-transform duration-300 ease-out border border-gray-200 dark:border-[#30363d]">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-[#30363d]">
              <h2 class="dark:text-white text-xl font-bold text-gray-900">Add to credit balance</h2>
    </div>

            <div class="p-6 overflow-y-auto">
        <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Amount to add</label>
          <div class="relative">
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-[#8b949e] pointer-events-none">$</span>
                  <input v-model="customCreditAmount" type="number" min="5" max="95"
                    class="w-full py-2 pl-8 pr-3 border border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-md"
                    placeholder="10" @input="validateAmount" />
          </div>
                <div class="mt-1 text-xs"
                  :class="amountError ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-[#8b949e]'">
                  {{ amountMessage }}
          </div>
        </div>

        <!-- Paddle Error message -->
        <div v-if="paddleError" class="mb-6">
          <div class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-4 border border-red-200 rounded-lg">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h4 class="dark:text-red-200 text-sm font-medium text-red-800">Paddle Error</h4>
                <p class="dark:text-red-300 mt-1 text-sm text-red-700">
                  {{ paddleError }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Credits Error message -->
        <div v-if="creditsStore.error" class="mb-6">
          <div class="bg-red-50 dark:bg-red-900/20 dark:border-red-800 p-4 border border-red-200 rounded-lg">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h4 class="dark:text-red-200 text-sm font-medium text-red-800">Payment Error</h4>
                <p class="dark:text-red-300 mt-1 text-sm text-red-700">
                  {{ creditsStore.error }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <div class="bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800 p-4 border border-blue-200 rounded-lg">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <h4 class="dark:text-blue-200 text-sm font-medium text-blue-800">Secure Payment with Paddle</h4>
                <p class="dark:text-blue-300 mt-1 text-sm text-blue-700">
                  You'll be redirected to Paddle's secure checkout to complete your payment.
                  Credits will be added to your account automatically after successful payment.
                </p>
              </div>
            </div>
          </div>
        </div>

            <div class="px-6 py-4 border-t rounded-b-lg border-gray-200 dark:border-[#30363d] dark:bg-[#161b22]">
        <div class="flex justify-end space-x-4">
          <button class="btn-secondary" @click="showBuyCreditsModal = false" :disabled="creditsStore.checkoutLoading || paddleLoading">Cancel</button>
                <button class="btn-primary" :disabled="!isValidAmount || creditsStore.checkoutLoading || paddleLoading || !paddleInitialized"
                  @click="processCustomPayment">
            <span v-if="paddleLoading">Initializing Paddle...</span>
            <span v-else-if="creditsStore.checkoutLoading">Creating checkout...</span>
            <span v-else-if="!paddleInitialized">Paddle not ready</span>
            <span v-else>Continue to Payment</span>
          </button>
        </div>
      </div>
    </div>
        </div>
      </Transition>

    <!-- Add Payment Method Modal -->
      <Transition name="modal">
        <div v-if="showAddPaymentMethod"
          class="backdrop-blur-sm fixed inset-0 z-50 flex items-center justify-center overflow-hidden transition-all duration-300">
          <div
            class="w-full max-w-md max-h-[90vh] mx-4 bg-white dark:bg-[#161b22] rounded-lg shadow-xl flex flex-col transform transition-transform duration-300 ease-out border border-gray-200 dark:border-[#30363d]">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-[#30363d]">
              <h2 class="dark:text-white text-xl font-bold text-gray-900">Add payment method</h2>
            </div>

            <div class="p-6 overflow-y-auto">
        <p class="mb-4 text-sm text-gray-600 dark:text-[#8b949e]">
                Add your credit card details below. This card will be saved to your account and can be removed at any
                time.
        </p>

        <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Card information</label>
          <div class="overflow-hidden border border-gray-300 rounded-md dark:border-[#30363d]">
                  <input type="text" placeholder="Card number"
                    class="w-full px-3 py-2 border-b border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            <div class="flex">
                    <input type="text" placeholder="MM / YY"
                      class="w-1/2 px-3 py-2 border-r border-gray-300 dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                    <input type="text" placeholder="CVC"
                      class="w-1/2 px-3 py-2 dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
          </div>
        </div>

        <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Name on card</label>
                <input type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>

        <div class="mb-6">
                <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-[#c9d1d9]">Billing address</label>
                <select
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option>United States</option>
            <option>Canada</option>
            <option>United Kingdom</option>
          </select>
                <input type="text" placeholder="Address line 1"
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                <input type="text" placeholder="Address line 2"
                  class="w-full px-3 py-2 mb-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
          <div class="grid grid-cols-2 gap-2">
                  <input type="text" placeholder="City"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                  <input type="text" placeholder="Postal code"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-[#30363d] dark:bg-[#21262d] dark:text-white dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
                </div>
          </div>
        </div>

            <div class="px-6 py-4 border-t rounded-b-lg border-gray-200 dark:border-[#30363d] dark:bg-[#161b22]">
        <div class="flex justify-end space-x-4">
          <button class="btn-secondary" @click="showAddPaymentMethod = false">Cancel</button>
                <button class="btn-primary" @click="addPaymentMethod">
            Add payment method
          </button>
        </div>
      </div>
    </div>
  </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useSubscriptionStore } from '@/stores/subscription'
import { useCreditsStore } from '@/stores/credits'
import { usePaddle } from '@/lib/paddle'
import { format } from 'date-fns'

// Stores
const subscriptionStore = useSubscriptionStore()
const creditsStore = useCreditsStore()
const { isInitialized: paddleInitialized, isLoading: paddleLoading, error: paddleError } = usePaddle()

// State variables
const showBuyCreditsModal = ref(false)
const showAddPaymentMethod = ref(false)
const customCreditAmount = ref(0)
const autoRecharge = ref(false)
const amountError = ref(false)
const amountMessage = ref('Enter an amount between $5 and $95')

// Computed properties
const userSubscription = subscriptionStore.userSubscription
const currentPlan = subscriptionStore.currentPlan
const hasActiveFreeTrialPlan = Boolean(userSubscription?.status === 'active' && currentPlan?.name === 'Free Trial')
const hasPaymentMethod = Boolean(creditsStore.paymentMethod)
const isValidAmount = customCreditAmount.value >= 5 && customCreditAmount.value <= 95
const creditBalance = computed(() => {
  return creditsStore.credits?.balance.toFixed(2) || '0.00'
})

// Methods
function formatDate(dateString?: string) {
  if (!dateString) return 'N/A'
  return format(new Date(dateString), 'MMM d, yyyy')
}


async function processCustomPayment() {
  // Create Paddle checkout for credit purchase
  if (customCreditAmount.value > 0) {
    const success = await creditsStore.createPaddleCheckout(customCreditAmount.value)
    if (success) {
      // User will be redirected to Paddle checkout
      showBuyCreditsModal.value = false
      customCreditAmount.value = 0
    }
    // If there's an error, it will be shown in the store's error state
  }
}

function addPaymentMethod() {
  // Simulate adding a payment method
  showAddPaymentMethod.value = false
  creditsStore.addPaymentMethod()
}

function validateAmount() {
  const amount = customCreditAmount.value
  if (amount < 5) {
    amountError.value = true
    amountMessage.value = 'Minimum amount is $5'
  } else if (amount > 95) {
    amountError.value = true
    amountMessage.value = 'Maximum amount is $95'
  } else {
    amountError.value = false
    amountMessage.value = 'Enter an amount between $5 and $95'
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Check for payment success/cancel in URL params
  const urlParams = new URLSearchParams(window.location.search)
  const success = urlParams.get('success')
  const cancelled = urlParams.get('cancelled')

  if (success === 'true') {
    // Payment successful - refresh credits and show success message
    await creditsStore.fetchCredits()
    // You could show a success toast here
    console.log('Payment successful! Credits have been added to your account.')

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname)
  } else if (cancelled === 'true') {
    // Payment cancelled - show message
    console.log('Payment was cancelled.')

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname)
  }

  // Fetch data on component mount
  subscriptionStore.fetchUserSubscription()
  creditsStore.fetchCredits()
})
</script>

<style scoped>
.card {
  @apply bg-white dark:bg-[#161b22] shadow rounded-lg p-6 border border-gray-200 dark:border-[#30363d];
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded transition-colors dark:bg-[#21262d] dark:hover:bg-[#30363d] dark:text-[#c9d1d9];
}

/* Hide number input spinners */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Modal transition */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.5s ease, transform 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>