import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'

interface SubscriptionPlan {
  id: string
  name: string
  description: string
  monthly_price: number
  annual_price: number | null
  transcription_minutes: number
  input_tokens: number
  output_tokens: number
  tokens?: number
  is_active: boolean
  created_at: string
}

interface UserSubscription {
  id: string
  user_id: string
  plan_id: string
  status: string
  current_period_start: string
  current_period_end: string
  cancel_at_period_end: boolean
  created_at: string
  stripe_subscription_id: string | null
}

interface Quota {
  id: string
  user_id: string
  subscription_id: string
  service: string
  used_amount: number
  total_amount: number
  reset_date: string
  created_at: string
}

export const useSubscriptionStore = defineStore('subscription', () => {
  const availablePlans = ref<SubscriptionPlan[]>([])
  const userSubscription = ref<UserSubscription | null>(null)
  const quotas = ref<Quota[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed property to get the current plan
  const currentPlan = computed(() => {
    if (!userSubscription.value || !availablePlans.value.length) return null

    return availablePlans.value.find(plan => plan.id === userSubscription.value?.plan_id) || null
  })

  // Computed property to check if user has an active subscription
  const hasActiveSubscription = computed(() => {
    if (!userSubscription.value) return false

    const isActive = userSubscription.value.status === 'active'
    const isNotExpired = new Date(userSubscription.value.current_period_end) > new Date()

    return isActive && isNotExpired
  })

  // Computed property to check if user has a subscription (active or expired)
  const hasSubscription = computed(() => {
    return userSubscription.value !== null
  })

  // Computed property to check if subscription is expired
  const isSubscriptionExpired = computed(() => {
    if (!userSubscription.value) return false

    return new Date(userSubscription.value.current_period_end) <= new Date()
  })

  // Fetch available subscription plans
  async function fetchAvailablePlans() {
    loading.value = true
    error.value = null

    try {
      const { data, error: fetchError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('monthly_price', { ascending: true })

      if (fetchError) {
        error.value = fetchError.message
        return false
      }

      availablePlans.value = data as SubscriptionPlan[]
      return true
    } catch (err) {
      console.error('Error fetching subscription plans:', err)
      error.value = 'Failed to fetch subscription plans'
      return false
    } finally {
      loading.value = false
    }
  }

  // Fetch user's current subscription
  async function fetchUserSubscription() {
    loading.value = true
    error.value = null

    try {
      // First try to get active subscription
      let { data, error: fetchError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('status', 'active')
        .order('current_period_end', { ascending: false })
        .limit(1)

      // If no active subscription found, get the most recent subscription
      if (!data || data.length === 0) {
        const { data: allData, error: allError } = await supabase
          .from('user_subscriptions')
          .select('*')
          .order('current_period_end', { ascending: false })
          .limit(1)

        data = allData
        fetchError = allError
      }

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
        error.value = fetchError.message
        return false
      }

      userSubscription.value = data && data.length > 0 ? data[0] as UserSubscription : null

      // If we have a subscription, fetch the quotas
      if (userSubscription.value) {
        await fetchQuotas()
      }

      return true
    } catch (err) {
      console.error('Error fetching user subscription:', err)
      error.value = 'Failed to fetch user subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Fetch quotas for the current user
  async function fetchQuotas() {
    loading.value = true
    error.value = null

    try {
      let quotaData: Quota[] = []
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        quotas.value = []
        return false
      }

      // Only fetch subscription quotas if we have an ACTIVE and NON-EXPIRED subscription
      if (userSubscription.value && hasActiveSubscription.value) {
        const { data: subscriptionQuotas, error: subError } = await supabase
          .from('quotas')
          .select('*')
          .eq('subscription_id', userSubscription.value.id)
          .gte('reset_date', new Date().toISOString()) // Only get non-expired quotas

        if (subError) {
          console.error('Error fetching subscription quotas:', subError)
        } else {
          quotaData = [...quotaData, ...(subscriptionQuotas as Quota[])]
        }
      }

      // Always fetch complimentary quotas (not linked to any subscription)
      const { data: complimentaryQuotas, error: compError } = await supabase
        .from('quotas')
        .select('*')
        .eq('user_id', user.id)
        .is('subscription_id', null) // Quotas not linked to a subscription
        .gte('reset_date', new Date().toISOString()) // Only get non-expired quotas

      if (compError) {
        console.error('Error fetching complimentary quotas:', compError)
      } else {
        quotaData = [...quotaData, ...(complimentaryQuotas as Quota[])]
      }

      quotas.value = quotaData
      console.log('Fetched quotas:', quotaData) // Debug log
      return true
    } catch (err) {
      console.error('Error fetching quotas:', err)
      error.value = 'Failed to fetch quotas'
      return false
    } finally {
      loading.value = false
    }
  }

  // Cancel subscription at the end of the current period
  async function cancelSubscription() {
    if (!userSubscription.value) return false

    loading.value = true
    error.value = null

    try {
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({ cancel_at_period_end: true })
        .eq('id', userSubscription.value.id)

      if (updateError) {
        error.value = updateError.message
        return false
      }

      // Update local state
      if (userSubscription.value) {
        userSubscription.value.cancel_at_period_end = true
      }

      return true
    } catch (err) {
      console.error('Error canceling subscription:', err)
      error.value = 'Failed to cancel subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Resume a canceled subscription
  async function resumeSubscription() {
    if (!userSubscription.value) return false

    loading.value = true
    error.value = null

    try {
      const { error: updateError } = await supabase
        .from('user_subscriptions')
        .update({ cancel_at_period_end: false })
        .eq('id', userSubscription.value.id)

      if (updateError) {
        error.value = updateError.message
        return false
      }

      // Update local state
      if (userSubscription.value) {
        userSubscription.value.cancel_at_period_end = false
      }

      return true
    } catch (err) {
      console.error('Error resuming subscription:', err)
      error.value = 'Failed to resume subscription'
      return false
    } finally {
      loading.value = false
    }
  }

  // Get quota usage for a specific service
  function getQuotaUsage(service: string) {
    const quota = quotas.value.find(q => q.service === service)

    if (!quota) return { used: 0, total: 0, percentage: 0 }

    const used = quota.used_amount
    const total = quota.total_amount
    const percentage = total > 0 ? (used / total) * 100 : 0

    return { used, total, percentage }
  }

  return {
    availablePlans,
    userSubscription,
    quotas,
    loading,
    error,
    currentPlan,
    hasActiveSubscription,
    hasSubscription,
    isSubscriptionExpired,
    fetchAvailablePlans,
    fetchUserSubscription,
    fetchQuotas,
    cancelSubscription,
    resumeSubscription,
    getQuotaUsage
  }
})